# 快速测试 - 内置配置版本
# 如果外部配置有问题，使用这个版本

import sensor, image, time

print("=== Quick Test Dispatcher ===")

# 内置配置
CONFIG = {
    'active_mode': 'red_tracking',
    'red_tracking': {
        'threshold': (20, 100, 15, 127, 15, 127),
        'min_pixels': 50,
        'debug': True,
        'show_coordinates': True
    },
    'basic_camera': {
        'show_fps': True,
        'show_frame_count': True
    }
}

print("Config loaded. Active mode:", CONFIG['active_mode'])

# 简化的红色追踪模块
class SimpleRedTracker:
    def __init__(self, config):
        self.config = config
        self.frame_count = 0
        self.threshold = config.get('threshold', (20, 100, 15, 127, 15, 127))
        print("Red Tracker initialized with threshold:", self.threshold)
    
    def process(self, img):
        self.frame_count += 1
        
        # 查找红色区域
        blobs = img.find_blobs([self.threshold], pixels_threshold=50)
        
        if blobs:
            largest = max(blobs, key=lambda b: b.pixels())
            # 画绿色框
            img.draw_rectangle(largest.rect(), color=(0, 255, 0), thickness=3)
            # 画十字
            img.draw_cross(largest.cx(), largest.cy(), color=(0, 255, 0), size=15)
            # 显示坐标
            coord_text = "(" + str(largest.cx()) + "," + str(largest.cy()) + ")"
            img.draw_string(largest.cx() - 30, largest.cy() - 30, coord_text, color=(255, 255, 255))
            
            if self.frame_count % 10 == 0:
                print("RED at:", largest.cx(), largest.cy())
        
        # 显示状态
        status = "RED FOUND!" if blobs else "Looking..."
        img.draw_string(10, 10, status, color=(255, 0, 0) if blobs else (255, 255, 255))
        img.draw_string(10, 220, "Quick Test Mode", color=(255, 255, 0))

# 简化的基础相机模块
class SimpleCamera:
    def __init__(self, config):
        self.config = config
        self.frame_count = 0
        self.start_time = time.ticks_ms()
        print("Basic Camera initialized")
    
    def process(self, img):
        self.frame_count += 1
        
        # 显示帧数
        frame_text = "Frame: " + str(self.frame_count)
        img.draw_string(10, 10, frame_text, color=(255, 255, 255))
        
        # 显示FPS
        if self.frame_count > 30:
            current_time = time.ticks_ms()
            elapsed = time.ticks_diff(current_time, self.start_time)
            fps = (self.frame_count * 1000) // elapsed if elapsed > 0 else 0
            fps_text = "FPS: " + str(fps)
            img.draw_string(10, 30, fps_text, color=(0, 255, 0))
        
        img.draw_string(10, 220, "Basic Camera Mode", color=(255, 255, 0))

# 主程序
def main():
    print("Initializing camera...")
    sensor.reset()
    sensor.set_pixformat(sensor.RGB565)
    sensor.set_framesize(sensor.QVGA)
    sensor.skip_frames(time=2000)
    sensor.set_auto_gain(False)
    sensor.set_auto_whitebal(False)
    print("Camera ready!")
    
    # 创建模块
    active_mode = CONFIG['active_mode']
    if active_mode == 'red_tracking':
        module = SimpleRedTracker(CONFIG['red_tracking'])
    else:
        module = SimpleCamera(CONFIG['basic_camera'])
    
    print("Starting", active_mode, "mode...")
    print("Press Ctrl+C to stop")
    
    frame_count = 0
    try:
        while True:
            img = sensor.snapshot()
            frame_count += 1
            module.process(img)
            
            # 每100帧显示一次状态
            if frame_count % 100 == 0:
                print("Running... Frame:", frame_count)
                
    except KeyboardInterrupt:
        print("Stopped by user")

if __name__ == "__main__":
    main()
