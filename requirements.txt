# OpenMV-H7 R2 视觉开发框架依赖包

# 核心依赖
numpy>=1.19.0
Pillow>=8.0.0
opencv-python>=4.5.0

# 配置管理
PyYAML>=5.4.0
configparser>=5.0.0

# 日志和调试
colorlog>=6.0.0
rich>=10.0.0

# 数据处理
scipy>=1.7.0
scikit-image>=0.18.0

# 机器学习
tensorflow-lite>=2.5.0
onnxruntime>=1.8.0

# 通信协议
pyserial>=3.5
requests>=2.25.0
websocket-client>=1.0.0

# 图像处理
imageio>=2.9.0
matplotlib>=3.3.0

# 工具库
tqdm>=4.60.0
click>=8.0.0

# 开发工具 (可选)
pytest>=6.0.0
pytest-cov>=2.0.0
black>=21.0.0
flake8>=3.8.0
mypy>=0.800

# 文档生成 (可选)
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0
myst-parser>=0.15.0
