# 简化测试版本 - 用于验证配置系统
# 如果main_dispatcher.py无法显示输出，请运行这个文件

import time

print("=== OpenMV Dispatcher Test ===")

# 测试配置加载
config = None
try:
    from config import get_config, validate_config
    print("✅ Config system loaded successfully")

    config = get_config()
    print("✅ Config loaded successfully")
    print("Active mode:", config['active_mode'])
    print("Available modes:", list(config['modes'].keys()))

    # 测试配置验证
    if validate_config(config):
        print("✅ Config validation passed")
    else:
        print("❌ Config validation failed")

    # 显示当前配置
    active_mode = config['active_mode']
    mode_config = config['modes'][active_mode]
    print("\nCurrent mode config:")
    for key, value in mode_config.items():
        print("  " + str(key) + ":", str(value))

except ImportError:
    print("❌ Config system not found")
    config = {'active_mode': 'unknown'}
except Exception as e:
    print("❌ Config error:", str(e))
    config = {'active_mode': 'error'}

# 测试模拟运行
print("\n=== Simulating Dispatcher Run ===")
if config:
    for i in range(5):
        print("Frame", i+1, "- Mode:", config.get('active_mode', 'unknown'))
        time.sleep(1)
else:
    print("Config not available, skipping simulation")

print("\n=== Test Complete ===")
print("If you can see this, the basic system is working!")
print("Now try modifying config.py and run this test again.")

# 创建状态文件
try:
    with open("test_status.txt", "w") as f:
        f.write("Test completed successfully\n")
        if config:
            f.write("Active mode: " + str(config.get('active_mode')) + "\n")
        else:
            f.write("Config not available\n")
        f.write("Time: " + str(time.time()) + "\n")
    print("Status file created: test_status.txt")
except Exception as e:
    print("Could not create status file:", str(e))
