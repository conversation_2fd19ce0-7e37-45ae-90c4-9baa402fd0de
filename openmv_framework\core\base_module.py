#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模块基类

定义所有模块的标准接口和基础功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class BaseModule(ABC):
    """模块抽象基类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化模块
        
        Args:
            config: 模块配置字典
        """
        self.config = config or {}
        self.name = self.__class__.__name__
        self.is_initialized = False
        self.status = "created"
        
    @abstractmethod
    def init(self) -> bool:
        """
        模块初始化
        
        Returns:
            bool: 初始化是否成功
        """
        pass
        
    @abstractmethod
    def configure(self, params: Dict[str, Any]) -> bool:
        """
        配置模块参数
        
        Args:
            params: 配置参数字典
            
        Returns:
            bool: 配置是否成功
        """
        pass
        
    @abstractmethod
    def process(self, input_data: Any) -> Any:
        """
        处理数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        pass
        
    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            状态信息字典
        """
        return {
            "name": self.name,
            "initialized": self.is_initialized,
            "status": self.status,
            "config": self.config
        }
        
    def cleanup(self):
        """清理模块资源"""
        self.is_initialized = False
        self.status = "cleaned"
        
    def set_config(self, key: str, value: Any):
        """
        设置配置项
        
        Args:
            key: 配置键
            value: 配置值
        """
        self.config[key] = value
        
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self.config.get(key, default)
