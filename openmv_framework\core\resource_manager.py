#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
资源管理器

管理系统资源，包括内存、缓冲区等。
"""

import gc
from typing import Dict, Any, Optional

class ResourceManager:
    """资源管理器"""
    
    def __init__(self):
        """初始化资源管理器"""
        self.memory_pools = {}
        self.allocated_resources = {}
        self.resource_limits = {
            'max_memory': 1024 * 1024,  # 1MB默认限制
            'max_buffers': 10
        }
        
    def allocate_memory(self, size: int, pool_name: str = "default") -> Optional[bytearray]:
        """
        分配内存
        
        Args:
            size: 内存大小
            pool_name: 内存池名称
            
        Returns:
            分配的内存对象或None
        """
        try:
            if self.get_available_memory() < size:
                self.cleanup_memory()
                
            if self.get_available_memory() < size:
                print(f"内存不足，无法分配 {size} 字节")
                return None
                
            memory = bytearray(size)
            
            if pool_name not in self.memory_pools:
                self.memory_pools[pool_name] = []
            
            self.memory_pools[pool_name].append(memory)
            return memory
            
        except Exception as e:
            print(f"内存分配失败: {e}")
            return None
    
    def free_memory(self, memory: bytearray, pool_name: str = "default"):
        """
        释放内存
        
        Args:
            memory: 要释放的内存对象
            pool_name: 内存池名称
        """
        try:
            if pool_name in self.memory_pools:
                if memory in self.memory_pools[pool_name]:
                    self.memory_pools[pool_name].remove(memory)
                    del memory
        except Exception as e:
            print(f"内存释放失败: {e}")
    
    def cleanup_memory(self):
        """清理内存"""
        try:
            # 强制垃圾回收
            gc.collect()
            
            # 清理空的内存池
            empty_pools = [name for name, pool in self.memory_pools.items() if not pool]
            for name in empty_pools:
                del self.memory_pools[name]
                
        except Exception as e:
            print(f"内存清理失败: {e}")
    
    def get_memory_usage(self) -> Dict[str, int]:
        """
        获取内存使用情况
        
        Returns:
            内存使用统计
        """
        usage = {}
        total_size = 0
        
        for pool_name, pool in self.memory_pools.items():
            pool_size = sum(len(mem) for mem in pool)
            usage[pool_name] = pool_size
            total_size += pool_size
            
        usage['total'] = total_size
        return usage
    
    def get_available_memory(self) -> int:
        """
        获取可用内存
        
        Returns:
            可用内存大小
        """
        used = self.get_memory_usage().get('total', 0)
        return self.resource_limits['max_memory'] - used
    
    def set_memory_limit(self, limit: int):
        """
        设置内存限制
        
        Args:
            limit: 内存限制大小
        """
        self.resource_limits['max_memory'] = limit
    
    def register_resource(self, name: str, resource: Any):
        """
        注册资源
        
        Args:
            name: 资源名称
            resource: 资源对象
        """
        self.allocated_resources[name] = resource
    
    def unregister_resource(self, name: str):
        """
        注销资源
        
        Args:
            name: 资源名称
        """
        if name in self.allocated_resources:
            del self.allocated_resources[name]
    
    def get_resource_status(self) -> Dict[str, Any]:
        """
        获取资源状态
        
        Returns:
            资源状态信息
        """
        return {
            'memory_usage': self.get_memory_usage(),
            'available_memory': self.get_available_memory(),
            'memory_limit': self.resource_limits['max_memory'],
            'registered_resources': list(self.allocated_resources.keys())
        }
