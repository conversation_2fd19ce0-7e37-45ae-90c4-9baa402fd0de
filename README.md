# OpenMV-H7 R2 视觉开发框架

一个专为OpenMV-H7 R2设计的低耦合、高扩展性视觉开发框架，支持快速构建机器视觉应用。

## 特性

- **低耦合设计**: 模块间依赖最小化，通过标准接口通信
- **高扩展性**: 支持插件化模块加载和功能扩展  
- **硬件优化**: 充分利用OpenMV-H7 R2硬件特性
- **易用性**: 提供简洁的Python API，降低开发门槛

## 硬件支持

- **处理器**: STM32H743VI ARM Cortex M7 @ 480MHz
- **内存**: 1MB SRAM, 2MB Flash
- **图像传感器**: MT9M114 (640x480, 40-80 FPS)
- **接口**: USB, SPI, I2C, CAN, UART, ADC/DAC
- **存储**: μSD卡支持

## 核心模块

- **Camera**: 相机控制和图像采集
- **Image-Processing**: 图像处理算法库
- **Machine-Learning**: TinyML模型推理
- **Feature-Detection**: 特征检测和分析
- **Barcodes**: 条码识别
- **April-Tags**: AprilTag检测和姿态估计
- **Communication**: WiFi/Bluetooth/RPC通信
- **Storage**: 数据存储和管理
- **Utils**: 通用工具和辅助功能

## 快速开始

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd openmv-h7-framework

# 安装依赖
pip install -r requirements.txt

# 安装框架
python setup.py install
```

### 基础使用

```python
from openmv_framework import OpenMVFramework
from openmv_framework.modules.camera import CameraModule

# 初始化框架
framework = OpenMVFramework()

# 加载相机模块
camera = CameraModule()
framework.register_module('camera', camera)

# 采集图像
image = camera.capture_image()
print(f"采集到图像: {image.width}x{image.height}")
```

## 项目结构

```
openmv_framework/
├── core/                 # 核心组件
│   ├── base_module.py    # 模块基类
│   ├── config_manager.py # 配置管理
│   ├── event_system.py   # 事件系统
│   └── logger.py         # 日志系统
├── modules/              # 功能模块
│   ├── camera/           # 相机模块
│   ├── image_processing/ # 图像处理
│   ├── machine_learning/ # 机器学习
│   ├── feature_detection/# 特征检测
│   ├── barcodes/         # 条码识别
│   ├── april_tags/       # AprilTag检测
│   ├── communication/    # 通信模块
│   ├── storage/          # 存储管理
│   └── utils/            # 工具模块
├── config/               # 配置文件
├── examples/             # 示例代码
└── docs/                 # 文档
```

## 示例应用

查看 `examples/` 目录获取更多示例：

- `basic/hello_camera.py` - 基础相机使用
- `basic/image_processing.py` - 图像处理示例
- `advanced/face_detection.py` - 人脸检测
- `advanced/object_tracking.py` - 物体跟踪

## 文档

- [架构设计](docs/architecture/) - 技术架构文档
- [API参考](docs/api/) - 详细API文档
- [教程](docs/tutorials/) - 使用教程
- [最佳实践](docs/best_practices.md) - 开发最佳实践

## 开发

### 环境要求

- Python 3.7+
- OpenMV IDE
- OpenMV-H7 R2 硬件

### 开发指南

1. Fork 项目
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 贡献

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解贡献指南。

## 支持

- 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)
- 讨论交流: [GitHub Discussions](https://github.com/your-repo/discussions)
- 邮件联系: <EMAIL>

## 更新日志

查看 [CHANGELOG.md](CHANGELOG.md) 了解版本更新历史。

---

**版权所有 © 2025 米醋电子工作室**
