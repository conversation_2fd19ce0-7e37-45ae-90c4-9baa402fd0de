{"framework": {"name": "OpenMV Framework", "version": "1.0.0", "debug": false, "log_level": "INFO"}, "hardware": {"board": "OpenMV-H7-R2", "processor": "STM32H743VI", "clock_speed": 480000000, "memory": {"sram": 1048576, "flash": 2097152}, "camera": {"sensor": "MT9M114", "max_resolution": [640, 480], "max_fps": 80}}, "modules": {"auto_load": ["camera", "image_processing", "storage", "utils"], "optional": ["machine_learning", "feature_detection", "barcodes", "april_tags", "communication"]}, "logging": {"enabled": true, "level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "logs/framework.log", "max_size": 10485760, "backup_count": 5}, "performance": {"memory_pool_size": 524288, "image_buffer_count": 3, "enable_optimization": true, "profile_enabled": false}}