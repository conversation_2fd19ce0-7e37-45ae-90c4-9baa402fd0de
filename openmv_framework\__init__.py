#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OpenMV-H7 R2 视觉开发框架

一个专为OpenMV-H7 R2设计的低耦合、高扩展性视觉开发框架。
"""

__version__ = '1.0.0'
__author__ = '米醋电子工作室'
__email__ = '<EMAIL>'
__license__ = 'MIT'

from .core.framework import OpenMVFramework
from .core.base_module import BaseModule
from .core.config_manager import ConfigManager
from .core.event_system import EventSystem
from .core.logger import Logger

# 导出主要类
__all__ = [
    'OpenMVFramework',
    'BaseModule', 
    'ConfigManager',
    'EventSystem',
    'Logger',
]

# 版本信息
def get_version():
    """获取框架版本信息"""
    return __version__

def get_info():
    """获取框架详细信息"""
    return {
        'name': 'OpenMV Framework',
        'version': __version__,
        'author': __author__,
        'email': __email__,
        'license': __license__,
        'description': 'OpenMV-H7 R2 低耦合视觉开发框架',
    }
