# OpenMV 红色追踪 - 独立版本
# 适用于OpenMV-H7 R2，在VSCode中使用OpenMV扩展运行

import sensor, image, time

# 红色阈值 (LAB色彩空间)
red_threshold = (20, 100, 15, 127, 15, 127)  # 红色范围
min_pixels = 100  # 最小像素数

# 初始化摄像头
print("Initializing Red Tracking...")
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time=2000)
sensor.set_auto_gain(False)
sensor.set_auto_whitebal(False)
print("✅ Red Tracking initialized successfully")

# 计数器和时间
frame_count = 0
start_time = time.ticks_ms()
objects_found = 0

print("🎯 Red Tracking ready! Show red objects to camera.")
print("🔍 Threshold:", red_threshold)
print("📏 Min pixels:", min_pixels)
print("=" * 50)

try:
    while True:
        img = sensor.snapshot()
        frame_count += 1
        current_time = time.ticks_ms()
        
        # 查找红色区域
        blobs = img.find_blobs([red_threshold], pixels_threshold=min_pixels, area_threshold=min_pixels)
        
        # 重置当前帧的对象计数
        current_objects = 0
        
        # 处理找到的红色区域
        for blob in blobs:
            current_objects += 1
            objects_found += 1
            
            # 绘制边界框
            img.draw_rectangle(blob.rect(), color=(0, 255, 0), thickness=2)
            
            # 绘制中心点
            cx = blob.cx()
            cy = blob.cy()
            img.draw_cross(cx, cy, color=(255, 0, 0), size=10, thickness=2)
            
            # 显示坐标信息
            coord_text = "(" + str(cx) + "," + str(cy) + ")"
            img.draw_string(cx - 30, cy - 20, coord_text, color=(255, 255, 255), scale=1)
            
            # 显示区域大小
            size_text = str(blob.pixels())
            img.draw_string(blob.x(), blob.y() - 15, size_text, color=(255, 255, 0), scale=1)
        
        # 显示帧数
        img.draw_string(10, 10, "Frame: " + str(frame_count), color=(255, 255, 255), scale=1)
        
        # 显示FPS
        elapsed = time.ticks_diff(current_time, start_time)
        if elapsed > 0:
            fps = frame_count * 1000 // elapsed
            img.draw_string(10, 30, "FPS: " + str(fps), color=(255, 255, 255), scale=1)
        
        # 显示当前帧检测到的对象数
        img.draw_string(10, 50, "Objects: " + str(current_objects), color=(0, 255, 255), scale=1)
        
        # 显示总检测数
        img.draw_string(10, 70, "Total: " + str(objects_found), color=(0, 255, 255), scale=1)
        
        # 显示模式信息
        img.draw_string(10, 220, "Mode: Red Tracking", color=(255, 0, 0), scale=1)

except KeyboardInterrupt:
    print("Red Tracking stopped")
    print("Total objects detected:", objects_found)
