# OpenMV Advanced Color Tracking
# 高级颜色追踪示例 - 支持多种颜色和参数调整

import sensor, image, time

# 预定义颜色阈值 (LAB色彩空间)
color_thresholds = {
    'red': (30, 100, 15, 127, 15, 127),      # 红色
    'green': (30, 100, -64, -8, -32, 32),    # 绿色  
    'blue': (0, 30, 0, 64, -128, 0),         # 蓝色
    'yellow': (60, 100, -10, 10, 20, 127),   # 黄色
}

# 当前追踪的颜色 (可以修改这里来改变追踪颜色)
current_color = 'red'
current_threshold = color_thresholds[current_color]

# 检测参数
min_pixels = 100    # 最小像素数
min_area = 100      # 最小区域面积

# 初始化相机
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time = 2000)
sensor.set_auto_gain(False)
sensor.set_auto_whitebal(False)

print("Advanced Color Tracking Started")
print("Current color:", current_color)
print("Threshold:", current_threshold)

frame_count = 0
start_time = time.ticks_ms()

while(True):
    img = sensor.snapshot()
    frame_count += 1
    
    # 查找指定颜色的区域
    blobs = img.find_blobs([current_threshold], 
                          pixels_threshold=min_pixels, 
                          area_threshold=min_area,
                          merge=True)  # 合并相邻的区域
    
    # 显示当前追踪的颜色
    color_text = "Tracking: " + current_color.upper()
    img.draw_string(10, 10, color_text, color=(255, 255, 255), scale=2)
    
    if blobs:
        # 按区域大小排序，处理最大的3个区域
        blobs = sorted(blobs, key=lambda b: b.pixels(), reverse=True)[:3]
        
        for i, blob in enumerate(blobs):
            # 为不同的目标使用不同颜色的框
            if i == 0:
                box_color = (0, 255, 0)    # 绿色框 - 最大目标
            elif i == 1:
                box_color = (255, 255, 0)  # 黄色框 - 第二大
            else:
                box_color = (255, 0, 255)  # 紫色框 - 第三大
            
            # 画矩形框
            img.draw_rectangle(blob.rect(), color=box_color, thickness=2)
            
            # 画中心十字
            img.draw_cross(blob.cx(), blob.cy(), color=box_color, size=8, thickness=2)
            
            # 显示目标编号和坐标
            target_text = "#%d (%d,%d)" % (i+1, blob.cx(), blob.cy())
            text_y = 30 + i * 15
            img.draw_string(10, text_y, target_text, color=box_color, scale=1)
            
            # 为最大目标显示详细信息
            if i == 0:
                # 计算目标的圆形度 (越接近1越圆)
                roundness = blob.roundness()
                
                # 显示大小和形状信息
                size_text = "Size: %d px" % blob.pixels()
                img.draw_string(10, 70, size_text, color=(255, 255, 255), scale=1)
                
                shape_text = "Round: %.2f" % roundness
                img.draw_string(10, 85, shape_text, color=(255, 255, 255), scale=1)
                
                # 根据圆形度判断形状
                if roundness > 0.8:
                    shape_type = "Circle"
                elif roundness > 0.5:
                    shape_type = "Oval"
                else:
                    shape_type = "Irregular"
                
                img.draw_string(10, 100, "Shape: " + shape_type, color=(255, 255, 255), scale=1)
    
    # 显示检测状态
    status_text = "Objects: %d" % len(blobs) if blobs else "No objects"
    img.draw_string(10, 200, status_text, color=(255, 0, 0) if blobs else (128, 128, 128), scale=2)
    
    # 计算并显示FPS
    if frame_count % 30 == 0:
        current_time = time.ticks_ms()
        elapsed = time.ticks_diff(current_time, start_time)
        fps = (frame_count * 1000) // elapsed if elapsed > 0 else 0
        print("FPS:", fps, "Objects detected:", len(blobs) if blobs else 0)
    
    # 显示FPS
    if frame_count > 30:
        current_time = time.ticks_ms()
        elapsed = time.ticks_diff(current_time, start_time)
        fps = (frame_count * 1000) // elapsed if elapsed > 0 else 0
        fps_text = "FPS: %d" % fps
        img.draw_string(200, 10, fps_text, color=(0, 255, 255), scale=1)
