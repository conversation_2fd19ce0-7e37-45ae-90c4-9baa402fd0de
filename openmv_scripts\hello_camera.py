# OpenMV Hello Camera - 基础相机脚本
# 适用于OpenMV-H7 R2

import sensor
import image
import time

# 相机初始化
print("Initializing camera...")
sensor.reset()                      # 重置相机
sensor.set_pixformat(sensor.RGB565) # 设置为RGB565格式
sensor.set_framesize(sensor.QVGA)   # 设置为QVGA分辨率 (320x240)
sensor.skip_frames(time=2000)       # 等待相机稳定

print("Hello OpenMV Camera!")
print("Camera ready, starting capture...")

# 主循环
while True:
    img = sensor.snapshot()          # 拍摄一张照片
    time.sleep_ms(30)               # 添加30ms延时，让Frame Buffer有时间显示
