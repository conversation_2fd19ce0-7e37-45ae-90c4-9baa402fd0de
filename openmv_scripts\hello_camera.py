#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OpenMV Hello Camera - 最简单的相机脚本
适用于OpenMV-H7 R2，在OpenMV IDE中运行
"""

import sensor
import image
import time

# 相机初始化
sensor.reset()                      # 重置相机
sensor.set_pixformat(sensor.RGB565) # 设置为RGB565格式
sensor.set_framesize(sensor.QVGA)   # 设置为QVGA分辨率 (320x240)
sensor.skip_frames(time=2000)       # 等待相机稳定

print("Hello OpenMV Camera!")
print("相机已就绪，开始拍摄...")

# 主循环
while True:
    img = sensor.snapshot()          # 拍摄一张照片
    # 图像会自动显示在OpenMV IDE的Frame Buffer中
