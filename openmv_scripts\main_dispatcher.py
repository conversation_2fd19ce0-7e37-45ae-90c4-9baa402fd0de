# OpenMV 统一调度器 - 简化版本
# 单一入口点，支持多种功能模块的切换
# 适用于OpenMV-H7 R2，在VSCode中使用OpenMV扩展运行

import sensor, image, time
import os

# 确保在正确的目录中运行
try:
    os.chdir('/flash')
except:
    pass

# 尝试导入配置文件
CONFIG_AVAILABLE = False
try:
    import config
    CONFIG_AVAILABLE = True
    print("✅ Config module loaded successfully")
except ImportError:
    print("❌ Config module not found, using default settings")

# ===== 基础模块类 =====
class SimpleModule:
    """简化的模块基类"""
    
    def __init__(self, config=None):
        self.name = "BaseModule"
        self.config = config or {}
        self.is_initialized = False
    
    def init(self):
        """初始化模块"""
        self.is_initialized = True
        return True
    
    def process(self, img):
        """处理图像 - 子类需要重写"""
        pass
    
    def cleanup(self):
        """清理资源"""
        self.is_initialized = False
        print(f"Module {self.name} cleaned up")
    
    def configure(self, new_config):
        """重新配置模块"""
        self.config.update(new_config)

# ===== 基础相机模块 =====
class BasicCameraModule(SimpleModule):
    """基础相机功能模块"""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.name = "BasicCamera"
        self.frame_count = 0
        self.start_time = time.ticks_ms()
    
    def init(self):
        """初始化基础相机"""
        print("Basic Camera Module - Initializing...")
        super().init()
        print("Basic Camera Module - Ready!")
        return True
    
    def process(self, img):
        """处理基础相机显示"""
        self.frame_count += 1
        current_time = time.ticks_ms()
        
        # 显示帧数
        if self.config.get('show_frame_count', True):
            img.draw_string(10, 10, "Frame: " + str(self.frame_count), color=(255, 255, 255), scale=1)
        
        # 显示FPS
        if self.config.get('show_fps', True):
            elapsed = time.ticks_diff(current_time, self.start_time)
            if elapsed > 0:
                fps = self.frame_count * 1000 // elapsed
                img.draw_string(10, 30, "FPS: " + str(fps), color=(255, 255, 255), scale=1)
        
        # 显示时间戳
        if self.config.get('show_timestamp', False):
            img.draw_string(10, 50, "Time: " + str(current_time), color=(255, 255, 255), scale=1)
        
        # 显示模式信息
        img.draw_string(10, 220, "Mode: Basic Camera", color=(255, 255, 0), scale=1)
    
    def cleanup(self):
        """清理基础相机模块资源"""
        super().cleanup()
        self.frame_count = 0
        print("Basic Camera Module - Cleaned up")

# ===== 红色追踪模块 =====
class RedTrackingModule(SimpleModule):
    """红色物体追踪模块"""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.name = "RedTracking"
        self.threshold = self.config.get('threshold', (20, 100, 15, 127, 15, 127))
        self.min_pixels = self.config.get('min_pixels', 100)
        self.debug = self.config.get('debug', True)
    
    def init(self):
        """初始化红色追踪"""
        print("Red Tracking Module - Initializing...")
        super().init()
        print("Red Tracking Module - Ready!")
        print("Threshold:", self.threshold)
        return True
    
    def process(self, img):
        """处理红色追踪"""
        # 查找红色区域
        blobs = img.find_blobs([self.threshold], pixels_threshold=self.min_pixels, area_threshold=self.min_pixels)
        
        if blobs:
            # 找到最大的红色区域
            largest_blob = max(blobs, key=lambda b: b.pixels())
            
            # 绘制边框
            img.draw_rectangle(largest_blob.rect(), color=(0, 255, 0), thickness=2)
            img.draw_cross(largest_blob.cx(), largest_blob.cy(), color=(0, 255, 0), size=5, thickness=2)
            
            # 显示坐标信息
            if self.config.get('show_coordinates', True):
                coord_text = "X:" + str(largest_blob.cx()) + " Y:" + str(largest_blob.cy())
                img.draw_string(10, 10, coord_text, color=(255, 255, 255), scale=1)
            
            # 显示状态
            img.draw_string(10, 30, "RED FOUND!", color=(255, 0, 0), scale=2)
        else:
            # 未找到红色物体
            img.draw_string(10, 30, "Searching...", color=(255, 255, 255), scale=1)
        
        # 显示模式信息
        img.draw_string(10, 220, "Mode: Red Tracking", color=(255, 255, 0), scale=1)
    
    def cleanup(self):
        """清理红色追踪模块资源"""
        super().cleanup()
        print("Red Tracking Module - Cleaned up")

# ===== 主调度器类 =====
class SimpleDispatcher:
    """简化的功能调度器"""
    
    def __init__(self):
        self.current_module = None
        self.sensor_initialized = False
        self.modules = {}
        self.config = {}
        
        # 注册可用模块
        self.register_modules()
        
        # 加载配置
        self.load_config()
    
    def register_modules(self):
        """注册所有可用模块"""
        self.modules = {
            'basic_camera': BasicCameraModule,
            'red_tracking': RedTrackingModule
        }
        print("Registered modules:", list(self.modules.keys()))
    
    def load_config(self):
        """加载配置"""
        if CONFIG_AVAILABLE:
            try:
                # 强制重新导入配置模块
                import sys
                if 'config' in sys.modules:
                    del sys.modules['config']

                import config
                self.config = config.get_config()
                print("✅ Configuration loaded successfully")
                print("Active mode:", self.config.get('active_mode'))
                print("Config content:", self.config)  # 调试信息
            except Exception as e:
                print("❌ Error loading config:", str(e))
                self.config = self.get_default_config()
        else:
            print("❌ Config module not available, using defaults")
            self.config = self.get_default_config()
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            'active_mode': 'basic_camera',
            'global_debug': True,
            'basic_camera': {
                'show_frame_count': True,
                'show_fps': True,
                'show_timestamp': False
            },
            'red_tracking': {
                'threshold': (20, 100, 15, 127, 15, 127),
                'min_pixels': 100,
                'debug': True,
                'show_coordinates': True
            }
        }
    
    def init_sensor(self):
        """初始化摄像头"""
        if self.sensor_initialized:
            return True
        
        try:
            sensor.reset()
            sensor.set_pixformat(sensor.RGB565)
            sensor.set_framesize(sensor.QVGA)
            sensor.skip_frames(time=2000)
            sensor.set_auto_gain(False)
            sensor.set_auto_whitebal(False)
            self.sensor_initialized = True
            print("✅ Camera initialized successfully")
            return True
        except Exception as e:
            print("❌ Camera initialization failed:", str(e))
            return False
    
    def switch_mode(self, mode_name):
        """切换功能模式"""
        print("🔄 Switching to mode:", mode_name)
        
        # 清理当前模块
        if self.current_module:
            self.current_module.cleanup()
            self.current_module = None
        
        # 创建新模块
        module_class = self.modules.get(mode_name)
        if module_class:
            module_config = self.config.get(mode_name, {})
            self.current_module = module_class(module_config)
            if self.current_module.init():
                print("✅ Module", mode_name, "initialized successfully")
                return True
            else:
                print("❌ Failed to initialize module:", mode_name)
                return False
        else:
            print("❌ Unknown module:", mode_name)
            return False
    
    def run(self):
        """运行主循环"""
        print("=" * 50)
        print("🚀 OpenMV Simple Dispatcher Starting...")
        print("=" * 50)
        
        # 初始化摄像头
        if not self.init_sensor():
            print("❌ Failed to initialize camera, exiting...")
            return
        
        # 重新加载配置确保最新
        self.load_config()

        # 初始化第一个模块
        active_mode = self.config.get('active_mode', 'basic_camera')
        print("🎯 Starting with mode:", active_mode)
        if not self.switch_mode(active_mode):
            print("❌ Failed to initialize first module, exiting...")
            return
        
        print("🎯 Dispatcher ready! Press Ctrl+C to stop.")
        print("=" * 50)
        
        # 主循环
        try:
            while True:
                img = sensor.snapshot()
                
                # 处理当前模块
                if self.current_module and self.current_module.is_initialized:
                    self.current_module.process(img)
                
                # 显示调度器信息
                dispatcher_info = "Dispatcher: " + str(self.config.get('active_mode', 'unknown'))
                img.draw_string(200, 220, dispatcher_info, color=(128, 128, 128), scale=1)
                
        except KeyboardInterrupt:
            print("Stopping dispatcher...")
            if self.current_module:
                self.current_module.cleanup()
            print("Dispatcher stopped")

# ===== 主程序入口 =====
if __name__ == "__main__":
    # 创建并运行调度器
    dispatcher = SimpleDispatcher()
    dispatcher.run()
