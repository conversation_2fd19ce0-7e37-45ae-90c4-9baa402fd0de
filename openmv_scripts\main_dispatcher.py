# OpenMV Main Dispatcher - 统一调度器
# 单一入口点，支持多种功能模块的热切换
# 适用于OpenMV-H7 R2，在VSCode中使用OpenMV扩展运行

import sensor, image, time

# ===== 简化的模块基类 =====
class SimpleModule:
    """OpenMV兼容的简化模块基类"""
    
    def __init__(self, config):
        self.config = config or {}
        self.name = self.__class__.__name__
        self.is_initialized = False
    
    def init(self):
        """模块初始化"""
        self.is_initialized = True
        return True
    
    def process(self, img):
        """处理图像 - 子类必须实现"""
        pass
    
    def cleanup(self):
        """清理资源"""
        self.is_initialized = False
    
    def configure(self, params):
        """更新配置"""
        if params:
            self.config.update(params)

# ===== 基础相机模块 =====
class BasicCameraModule(SimpleModule):
    """基础相机功能模块"""
    
    def init(self):
        super().init()
        self.frame_count = 0
        self.start_time = time.ticks_ms()
        print("Basic Camera Module - Ready!")
        return True
    
    def process(self, img):
        self.frame_count += 1
        
        # 显示帧数
        if self.config.get('show_frame_count', True):
            frame_text = "Frame: " + str(self.frame_count)
            img.draw_string(10, 10, frame_text, color=(255, 255, 255), scale=2)
        
        # 计算和显示FPS
        if self.config.get('show_fps', True) and self.frame_count > 30:
            current_time = time.ticks_ms()
            elapsed = time.ticks_diff(current_time, self.start_time)
            fps = (self.frame_count * 1000) // elapsed if elapsed > 0 else 0
            fps_text = "FPS: " + str(fps)
            img.draw_string(10, 30, fps_text, color=(0, 255, 0), scale=2)
        
        # 显示模式信息
        img.draw_string(10, 220, "Mode: Basic Camera", color=(255, 255, 0), scale=1)

# ===== 红色追踪模块 =====
class RedTrackingModule(SimpleModule):
    """红色物体追踪模块"""
    
    def init(self):
        super().init()
        self.frame_count = 0
        # 默认红色阈值
        self.threshold = self.config.get('threshold', (20, 100, 15, 127, 15, 127))
        self.min_pixels = self.config.get('min_pixels', 50)
        self.debug = self.config.get('debug', True)
        print("Red Tracking Module - Ready!")
        print("Threshold:", self.threshold)
        return True
    
    def process(self, img):
        self.frame_count += 1
        
        # 查找红色区域
        blobs = img.find_blobs([self.threshold], 
                              pixels_threshold=self.min_pixels, 
                              area_threshold=self.min_pixels)
        
        # 调试信息
        if self.debug and self.frame_count % 30 == 0:
            print("Frame:", self.frame_count, "Blobs found:", len(blobs) if blobs else 0)
        
        if blobs:
            # 找到最大的红色区域
            largest_blob = max(blobs, key=lambda b: b.pixels())
            
            # 画绿色矩形框
            img.draw_rectangle(largest_blob.rect(), color=(0, 255, 0), thickness=3)
            
            # 画中心十字
            img.draw_cross(largest_blob.cx(), largest_blob.cy(), color=(0, 255, 0), size=15, thickness=3)
            
            # 显示坐标
            if self.config.get('show_coordinates', True):
                coord_text = "(" + str(largest_blob.cx()) + "," + str(largest_blob.cy()) + ")"
                img.draw_string(largest_blob.cx() - 30, largest_blob.cy() - 30, coord_text, 
                               color=(255, 255, 255), scale=2)
            
            # 调试信息
            if self.debug and self.frame_count % 10 == 0:
                print("RED DETECTED at:", largest_blob.cx(), largest_blob.cy(), "Size:", largest_blob.pixels())
        
        # 显示状态
        status = "RED FOUND!" if blobs else "Looking for red..."
        img.draw_string(10, 10, status, color=(255, 0, 0) if blobs else (255, 255, 255), scale=2)
        
        # 显示模式信息
        img.draw_string(10, 220, "Mode: Red Tracking", color=(255, 255, 0), scale=1)

# ===== 主调度器类 =====
class SimpleDispatcher:
    """简化的功能调度器"""
    
    def __init__(self):
        self.current_module = None
        self.sensor_initialized = False
        self.modules = {}
        self.config = {}
        
        # 注册可用模块
        self.register_modules()
        
        # 加载配置
        self.load_config()
    
    def register_modules(self):
        """注册所有可用模块"""
        self.modules = {
            'basic_camera': BasicCameraModule,
            'red_tracking': RedTrackingModule
        }
        print("Registered modules:", list(self.modules.keys()))
    
    def load_config(self):
        """加载配置 - 简化版本"""
        self.config = {
            'active_mode': 'red_tracking',  # 默认模式
            'basic_camera': {
                'show_fps': True,
                'show_frame_count': True
            },
            'red_tracking': {
                'threshold': (20, 100, 15, 127, 15, 127),
                'min_pixels': 50,
                'debug': True,
                'show_coordinates': True
            }
        }
        print("Config loaded. Active mode:", self.config['active_mode'])
    
    def init_sensor(self):
        """统一的相机初始化"""
        if not self.sensor_initialized:
            print("Initializing camera sensor...")
            sensor.reset()
            sensor.set_pixformat(sensor.RGB565)
            sensor.set_framesize(sensor.QVGA)
            sensor.skip_frames(time=2000)
            sensor.set_auto_gain(False)
            sensor.set_auto_whitebal(False)
            self.sensor_initialized = True
            print("Camera sensor initialized")
    
    def switch_mode(self, mode_name):
        """热切换功能模式"""
        print("Switching to mode:", mode_name)
        
        # 清理当前模块
        if self.current_module:
            self.current_module.cleanup()
            print("Previous module cleaned up")
        
        # 创建新模块
        module_class = self.modules.get(mode_name)
        if module_class:
            module_config = self.config.get(mode_name, {})
            self.current_module = module_class(module_config)
            if self.current_module.init():
                print("Module", mode_name, "initialized successfully")
                return True
            else:
                print("Failed to initialize module:", mode_name)
                return False
        else:
            print("Unknown module:", mode_name)
            return False
    
    def run(self):
        """主运行循环"""
        print("=" * 50)
        print("OpenMV Main Dispatcher Starting...")
        print("=" * 50)
        
        # 初始化相机
        self.init_sensor()
        
        # 切换到默认模式
        active_mode = self.config.get('active_mode', 'basic_camera')
        if not self.switch_mode(active_mode):
            print("Failed to start default mode, falling back to basic_camera")
            self.switch_mode('basic_camera')
        
        print("Starting main loop...")
        print("Press Ctrl+C to stop")
        
        # 主循环
        frame_count = 0
        try:
            while True:
                img = sensor.snapshot()
                frame_count += 1
                
                # 处理图像
                if self.current_module:
                    self.current_module.process(img)
                
                # 显示调度器信息
                dispatcher_info = "Dispatcher: Frame " + str(frame_count)
                img.draw_string(200, 220, dispatcher_info, color=(128, 128, 128), scale=1)
                
                # 简单的模式切换检查 (每100帧检查一次配置)
                if frame_count % 100 == 0:
                    self.check_config_change()
                
        except KeyboardInterrupt:
            print("Stopping dispatcher...")
            if self.current_module:
                self.current_module.cleanup()
            print("Dispatcher stopped")
    
    def check_config_change(self):
        """检查配置变化 - 简化版本"""
        # 这里可以实现配置文件热重载
        # 目前只是占位符
        pass

# ===== 主程序入口 =====
if __name__ == "__main__":
    # 创建并运行调度器
    dispatcher = SimpleDispatcher()
    dispatcher.run()
