# OpenMV Main Dispatcher - 统一调度器
# 单一入口点，支持多种功能模块的热切换
# 适用于OpenMV-H7 R2，在VSCode中使用OpenMV扩展运行

import sensor, image, time
import os

# 确保在正确的目录中运行
try:
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print("Working directory set to:", script_dir)
except:
    print("Could not change directory, using current path")

# 导入配置文件
try:
    # 尝试导入配置模块
    import config
    from config import get_config, validate_config
    CONFIG_AVAILABLE = True
    print("Configuration system loaded successfully")
except ImportError as e:
    CONFIG_AVAILABLE = False
    print("Warning: config.py not found, using built-in config")
    print("Import error:", str(e))

# ===== 简化的模块基类 =====
class SimpleModule:
    """OpenMV兼容的简化模块基类"""
    
    def __init__(self, config):
        self.config = config or {}
        self.name = self.__class__.__name__
        self.is_initialized = False

    def init(self):
        """模块初始化"""
        self.is_initialized = True
        return True

    def process(self, img):
        """处理图像 - 子类必须实现"""
        pass
    
    def cleanup(self):
        """清理资源"""
        self.is_initialized = False
        # 清理模块特定的属性
        if hasattr(self, 'frame_count'):
            self.frame_count = 0
        if hasattr(self, 'start_time'):
            delattr(self, 'start_time')
        print(f"Module {self.name} cleaned up")

    def configure(self, params):
        """更新配置"""
        if params:
            self.config.update(params)

# ===== 基础相机模块 =====
class BasicCameraModule(SimpleModule):
    """基础相机功能模块"""
    
    def init(self):
        super().init()
        self.frame_count = 0
        self.start_time = time.ticks_ms()
        print("Basic Camera Module - Ready!")
        return True
    
    def process(self, img):
        self.frame_count += 1

        # 获取显示样式配置
        text_color = self.config.get('text_color', (255, 255, 255))
        text_scale = self.config.get('text_scale', 2)

        # 显示帧数
        if self.config.get('show_frame_count', True):
            frame_text = "Frame: " + str(self.frame_count)
            img.draw_string(10, 10, frame_text, color=text_color, scale=text_scale)

        # 计算和显示FPS
        fps_update_frames = self.config.get('fps_update_frames', 30)
        if self.config.get('show_fps', True) and self.frame_count > fps_update_frames:
            current_time = time.ticks_ms()
            elapsed = time.ticks_diff(current_time, self.start_time)
            fps = (self.frame_count * 1000) // elapsed if elapsed > 0 else 0
            fps_text = "FPS: " + str(fps)
            img.draw_string(10, 30, fps_text, color=(0, 255, 0), scale=text_scale)

        # 显示时间戳
        if self.config.get('show_timestamp', False):
            timestamp = time.ticks_ms()
            time_text = "Time: " + str(timestamp)
            img.draw_string(10, 50, time_text, color=text_color, scale=text_scale)

        # 显示模式信息
        img.draw_string(10, 220, "Mode: Basic Camera", color=(255, 255, 0), scale=1)

    def cleanup(self):
        """清理基础相机模块资源"""
        super().cleanup()
        print("Basic Camera Module - Cleaned up")

# ===== 红色追踪模块 =====
class RedTrackingModule(SimpleModule):
    """红色物体追踪模块"""
    
    def init(self):
        super().init()
        self.frame_count = 0
        # 默认红色阈值
        self.threshold = self.config.get('threshold', (20, 100, 15, 127, 15, 127))
        self.min_pixels = self.config.get('min_pixels', 50)
        self.debug = self.config.get('debug', True)
        print("Red Tracking Module - Ready!")
        print("Threshold:", self.threshold)
        return True
    
    def process(self, img):
        self.frame_count += 1

        # 查找红色区域
        blobs = img.find_blobs([self.threshold],
                              pixels_threshold=self.min_pixels,
                              area_threshold=self.config.get('min_area', self.min_pixels))

        # 调试信息
        debug_interval = self.config.get('debug_interval', 30)
        if self.debug and self.frame_count % debug_interval == 0:
            print("Frame:", self.frame_count, "Blobs found:", len(blobs) if blobs else 0)
            if self.config.get('verbose_debug') and blobs:
                for i, blob in enumerate(blobs):
                    print("  Blob", i, "- Size:", blob.pixels(), "Pos:", blob.cx(), blob.cy())

        if blobs:
            # 限制检测目标数量
            max_targets = self.config.get('max_targets', 5)
            display_blobs = blobs[:max_targets] if len(blobs) > max_targets else blobs

            # 找到最大的红色区域
            largest_blob = max(display_blobs, key=lambda b: b.pixels())

            # 获取绘制样式配置
            box_color = self.config.get('box_color', (0, 255, 0))
            box_thickness = self.config.get('box_thickness', 3)
            cross_color = self.config.get('cross_color', (0, 255, 0))
            cross_size = self.config.get('cross_size', 15)
            cross_thickness = self.config.get('cross_thickness', 3)
            text_color = self.config.get('text_color', (255, 255, 255))
            text_scale = self.config.get('text_scale', 2)

            # 画矩形框
            img.draw_rectangle(largest_blob.rect(), color=box_color, thickness=box_thickness)

            # 画中心十字
            img.draw_cross(largest_blob.cx(), largest_blob.cy(),
                          color=cross_color, size=cross_size, thickness=cross_thickness)

            # 显示坐标
            if self.config.get('show_coordinates', True):
                coord_text = "(" + str(largest_blob.cx()) + "," + str(largest_blob.cy()) + ")"
                img.draw_string(largest_blob.cx() - 30, largest_blob.cy() - 30, coord_text,
                               color=text_color, scale=text_scale)

            # 显示大小信息
            if self.config.get('show_size_info', False):
                size_text = "Size:" + str(largest_blob.pixels())
                img.draw_string(largest_blob.cx() - 30, largest_blob.cy() + 20, size_text,
                               color=text_color, scale=text_scale)

            # 显示检测数量
            if self.config.get('show_detection_count', False) and len(blobs) > 1:
                count_text = "Count:" + str(len(blobs))
                img.draw_string(10, 50, count_text, color=text_color, scale=text_scale)

            # 调试信息
            if self.debug and self.frame_count % 10 == 0:
                print("RED DETECTED at:", largest_blob.cx(), largest_blob.cy(), "Size:", largest_blob.pixels())

        # 显示状态
        if self.config.get('show_status', True):
            status = "RED FOUND!" if blobs else "Looking for red..."
            found_color = self.config.get('found_color', (255, 0, 0))
            searching_color = self.config.get('searching_color', (255, 255, 255))
            status_color = found_color if blobs else searching_color
            img.draw_string(10, 10, status, color=status_color, scale=2)

        # 显示模式信息
        img.draw_string(10, 220, "Mode: Red Tracking", color=(255, 255, 0), scale=1)

    def cleanup(self):
        """清理红色追踪模块资源"""
        super().cleanup()
        # 清理追踪特定的属性
        if hasattr(self, 'threshold'):
            delattr(self, 'threshold')
        if hasattr(self, 'min_pixels'):
            delattr(self, 'min_pixels')
        if hasattr(self, 'debug'):
            delattr(self, 'debug')
        print("Red Tracking Module - Cleaned up")

# ===== 主调度器类 =====
class SimpleDispatcher:
    """简化的功能调度器"""
    
    def __init__(self):
        self.current_module = None
        self.sensor_initialized = False
        self.modules = {}
        self.config = {}
        
        # 注册可用模块
        self.register_modules()
        
        # 加载配置
        self.load_config()
    
    def register_modules(self):
        """注册所有可用模块"""
        self.modules = {
            'basic_camera': BasicCameraModule,
            'red_tracking': RedTrackingModule
        }
        print("Registered modules:", list(self.modules.keys()))
    
    def load_config(self):
        """加载配置 - 支持外部配置文件"""
        if CONFIG_AVAILABLE:
            try:
                full_config = get_config()
                self.config = {
                    'active_mode': full_config['active_mode'],
                    'config_check_interval': full_config.get('config_check_interval', 100),
                    'global_debug': full_config.get('global_debug', True)
                }
                # 加载各模式配置
                for mode_name, mode_config in full_config['modes'].items():
                    self.config[mode_name] = mode_config

                print("External config loaded successfully")
                print("Active mode:", self.config['active_mode'])
                if self.config.get('global_debug'):
                    print("Available modes:", list(full_config['modes'].keys()))

                # 启动时强制检查配置同步
                print("🔄 Performing startup config sync check...")
                return True
            except Exception as e:
                print("Error loading external config:", str(e))
                print("Falling back to built-in config")

        # 内置默认配置
        self.config = {
            'active_mode': 'red_tracking',
            'config_check_interval': 100,
            'global_debug': True,
            'basic_camera': {
                'show_fps': True,
                'show_frame_count': True,
                'text_color': (255, 255, 255),
                'text_scale': 2
            },
            'red_tracking': {
                'threshold': (20, 100, 15, 127, 15, 127),
                'min_pixels': 50,
                'debug': True,
                'show_coordinates': True,
                'box_color': (0, 255, 0),
                'box_thickness': 3,
                'cross_color': (0, 255, 0),
                'cross_size': 15,
                'cross_thickness': 3
            }
        }
        print("Built-in config loaded. Active mode:", self.config['active_mode'])
        return False
    
    def init_sensor(self):
        """统一的相机初始化"""
        if not self.sensor_initialized:
            print("Initializing camera sensor...")
            sensor.reset()
            sensor.set_pixformat(sensor.RGB565)
            sensor.set_framesize(sensor.QVGA)
            sensor.skip_frames(time=2000)
            sensor.set_auto_gain(False)
            sensor.set_auto_whitebal(False)
            self.sensor_initialized = True
            print("Camera sensor initialized")
    
    def switch_mode(self, mode_name):
        """热切换功能模式"""
        print("=" * 40)
        print("🔄 SWITCHING MODE:", mode_name)
        print("=" * 40)

        # 清理当前模块
        if self.current_module:
            print("🧹 Cleaning up previous module...")
            self.current_module.cleanup()
            self.current_module = None  # 确保完全清除引用
            print("✅ Previous module cleaned up")

        # 强制垃圾回收 (如果OpenMV支持)
        try:
            import gc
            gc.collect()
            print("🗑️ Garbage collection performed")
        except:
            pass

        # 创建新模块
        module_class = self.modules.get(mode_name)
        if module_class:
            print("🏗️ Creating new module:", mode_name)
            module_config = self.config.get(mode_name, {})
            self.current_module = module_class(module_config)

            print("🚀 Initializing new module...")
            if self.current_module.init():
                print("✅ Module", mode_name, "initialized successfully")
                print("=" * 40)
                return True
            else:
                print("❌ Failed to initialize module:", mode_name)
                self.current_module = None
                print("=" * 40)
                return False
        else:
            print("❌ Unknown module:", mode_name)
            print("=" * 40)
            return False
    
    def run(self):
        """主运行循环"""
        print("=" * 50)
        print("OpenMV Main Dispatcher Starting...")
        print("=" * 50)

        # 创建调试日志文件
        try:
            with open("dispatcher_log.txt", "w") as f:
                f.write("OpenMV Main Dispatcher Log\n")
                f.write("Active mode: " + str(self.config.get('active_mode')) + "\n")
                f.write("Available modules: " + str(list(self.modules.keys())) + "\n")
            print("Debug log created: dispatcher_log.txt")
        except:
            pass
        
        # 初始化相机
        self.init_sensor()
        
        # 切换到默认模式
        active_mode = self.config.get('active_mode', 'basic_camera')
        if not self.switch_mode(active_mode):
            print("Failed to start default mode, falling back to basic_camera")
            self.switch_mode('basic_camera')
        
        print("Starting main loop...")
        print("Press Ctrl+C to stop")
        
        # 主循环
        frame_count = 0
        try:
            while True:
                img = sensor.snapshot()
                frame_count += 1
                
                # 处理图像
                if self.current_module:
                    self.current_module.process(img)
                
                # 显示调度器信息
                dispatcher_info = "Dispatcher: Frame " + str(frame_count)
                img.draw_string(200, 220, dispatcher_info, color=(128, 128, 128), scale=1)
                
                # 配置变化检查 (支持热重载) - 强制每帧检查
                check_interval = self.config.get('config_check_interval', 10)  # 更频繁检查
                if frame_count % check_interval == 0:
                    if self.config.get('global_debug'):
                        print("🔄 Checking config at frame", frame_count)
                    self.check_config_change()
                
        except KeyboardInterrupt:
            print("Stopping dispatcher...")
            if self.current_module:
                self.current_module.cleanup()
            print("Dispatcher stopped")
    
    def check_config_change(self):
        """检查配置变化 - 支持热重载"""
        if not CONFIG_AVAILABLE:
            if self.config.get('global_debug'):
                print("Config not available, skipping check")
            return

        try:
            if self.config.get('global_debug'):
                print("Starting config check...")

            # OpenMV环境下更可靠的配置重载方法
            # 方法1: 尝试模块重载
            import sys
            if 'config' in sys.modules:
                del sys.modules['config']
                if self.config.get('global_debug'):
                    print("Deleted config module from cache")

            # 重新导入
            import config
            new_config = config.get_config()
            new_active_mode = new_config['active_mode']

            if self.config.get('global_debug'):
                print("Current mode:", self.config.get('active_mode'))
                print("New mode from config:", new_active_mode)

            # 如果模块重载失败，尝试直接读取文件
            if new_active_mode == self.config.get('active_mode'):
                if self.config.get('global_debug'):
                    print("🔍 Module reload might have failed, trying file-based detection")
                # 可能重载失败，尝试文件读取方法
                new_active_mode = self.read_config_from_file()
                if new_active_mode and new_active_mode != self.config.get('active_mode'):
                    print("📁 Using file-based config detection")
                    print("📁 File shows mode:", new_active_mode)
                else:
                    if self.config.get('global_debug'):
                        print("✅ No config change detected")
                    return  # 没有变化

            # 检查是否需要切换模式
            if new_active_mode != self.config.get('active_mode'):
                print("=" * 40)
                print("CONFIG CHANGE DETECTED!")
                print("Switching from", self.config.get('active_mode'), "to", new_active_mode)
                print("=" * 40)
                # 更新配置
                old_mode = self.config.get('active_mode')
                self.config['active_mode'] = new_active_mode

                # 尝试切换模式
                if self.switch_mode(new_active_mode):
                    print("✅ Mode switched successfully to", new_active_mode)
                    # 更新模式特定配置
                    if new_active_mode in self.config:
                        if self.current_module:
                            self.current_module.configure(self.config[new_active_mode])
                    print("=" * 40)
                else:
                    print("❌ Failed to switch mode, reverting to", old_mode)
                    self.config['active_mode'] = old_mode  # 回滚配置
                    print("=" * 40)
                return

            # 检查当前模式的参数是否有变化
            current_mode = self.config.get('active_mode')
            if current_mode in new_config['modes']:
                new_mode_config = new_config['modes'][current_mode]
                old_mode_config = self.config.get(current_mode, {})

                # 简单的配置比较 (检查关键参数)
                config_changed = False
                if current_mode == 'red_tracking':
                    # 检查红色追踪的关键参数
                    key_params = ['threshold', 'min_pixels', 'debug', 'show_coordinates']
                    for param in key_params:
                        if new_mode_config.get(param) != old_mode_config.get(param):
                            config_changed = True
                            break
                elif current_mode == 'basic_camera':
                    # 检查基础相机的关键参数
                    key_params = ['show_fps', 'show_frame_count', 'show_timestamp']
                    for param in key_params:
                        if new_mode_config.get(param) != old_mode_config.get(param):
                            config_changed = True
                            break

                if config_changed:
                    print("Config parameters changed, updating current module")
                    # 更新配置
                    self.config[current_mode] = new_mode_config
                    # 重新配置当前模块
                    if self.current_module:
                        self.current_module.configure(new_mode_config)
                        print("Module configuration updated")

        except Exception as e:
            if self.config.get('global_debug'):
                print("Config check error:", str(e))

    def read_config_from_file(self):
        """直接从文件读取配置 - 备用方法"""
        try:
            # 尝试多个可能的配置文件路径
            config_paths = ["config.py", "openmv_scripts/config.py", "./config.py"]
            content = None

            for path in config_paths:
                try:
                    # 尝试不同的编码方式
                    encodings = ['utf-8', 'gbk', 'cp1252', 'latin1']
                    for encoding in encodings:
                        try:
                            with open(path, "r", encoding=encoding) as f:
                                content = f.read()
                                if self.config.get('global_debug'):
                                    print("📁 Successfully read config from:", path, "with encoding:", encoding)
                                break
                        except UnicodeDecodeError:
                            continue
                    if content:
                        break
                except:
                    continue

            if not content:
                if self.config.get('global_debug'):
                    print("❌ Could not read config file from any path")
                return None

            # 简单的字符串匹配查找active_mode
            lines = content.split('\n')
            for line in lines:
                if "'active_mode'" in line and ":" in line:
                    # 提取模式值
                    parts = line.split(":")
                    if len(parts) > 1:
                        mode_part = parts[1].strip()
                        # 移除引号和逗号
                        mode_part = mode_part.replace("'", "").replace('"', "").replace(",", "").strip()
                        if mode_part in ['red_tracking', 'basic_camera']:
                            if self.config.get('global_debug'):
                                print("📁 Found active_mode in file:", mode_part)
                            return mode_part

            if self.config.get('global_debug'):
                print("❌ Could not find active_mode in config file")
            return None
        except Exception as e:
            if self.config.get('global_debug'):
                print("❌ Error reading config file:", str(e))
            return None

# ===== 主程序入口 =====
if __name__ == "__main__":
    # 创建并运行调度器
    dispatcher = SimpleDispatcher()
    dispatcher.run()
