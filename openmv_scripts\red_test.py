# Red Detection Test - 最简单的红色检测测试

import sensor, image, time

# 初始化相机
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time = 2000)

print("Red detection test started")

# 非常宽松的红色阈值
red_threshold = (10, 100, 10, 127, 10, 127)

while(True):
    img = sensor.snapshot()
    
    # 查找红色区域 - 非常宽松的参数
    blobs = img.find_blobs([red_threshold], pixels_threshold=20, area_threshold=20)
    
    if blobs:
        print("Found", len(blobs), "red objects")
        for blob in blobs:
            # 画红色矩形框
            img.draw_rectangle(blob.rect(), color=(255, 0, 0), thickness=2)
            # 画中心点
            img.draw_cross(blob.cx(), blob.cy(), color=(255, 0, 0), size=10, thickness=2)
    
    # 在屏幕上显示检测状态
    if blobs:
        img.draw_string(10, 10, "RED DETECTED!", color=(255, 0, 0), scale=2)
    else:
        img.draw_string(10, 10, "No red found", color=(255, 255, 255), scale=2)
