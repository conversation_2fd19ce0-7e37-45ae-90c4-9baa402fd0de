# OpenMV框架示例代码

本目录包含OpenMV框架的各种使用示例，帮助您快速上手和学习框架的功能。

## 目录结构

```
examples/
├── basic/              # 基础示例
│   ├── hello_framework.py    # 框架初始化示例
│   ├── camera_basic.py       # 相机基础使用
│   └── image_processing.py   # 图像处理示例
├── advanced/           # 高级示例
│   ├── face_detection.py     # 人脸检测
│   ├── object_tracking.py    # 物体跟踪
│   └── ml_inference.py       # 机器学习推理
└── applications/       # 完整应用示例
    ├── security_camera.py    # 安防摄像头
    └── barcode_scanner.py    # 条码扫描器
```

## 运行示例

### 基础示例

1. **框架初始化示例**
   ```bash
   python examples/basic/hello_framework.py
   ```

2. **相机基础使用**
   ```bash
   python examples/basic/camera_basic.py
   ```

3. **图像处理示例**
   ```bash
   python examples/basic/image_processing.py
   ```

### 高级示例

1. **人脸检测**
   ```bash
   python examples/advanced/face_detection.py
   ```

2. **物体跟踪**
   ```bash
   python examples/advanced/object_tracking.py
   ```

3. **机器学习推理**
   ```bash
   python examples/advanced/ml_inference.py
   ```

## 注意事项

1. 运行示例前请确保已正确安装OpenMV框架
2. 某些示例需要连接OpenMV-H7 R2硬件
3. 高级示例可能需要额外的模型文件或数据

## 学习路径

建议按以下顺序学习示例：

1. 从 `basic/hello_framework.py` 开始了解框架基础
2. 学习 `basic/camera_basic.py` 掌握相机操作
3. 通过 `basic/image_processing.py` 了解图像处理
4. 进阶到 `advanced/` 目录学习复杂应用
5. 参考 `applications/` 目录构建完整项目

## 获取帮助

如果在运行示例时遇到问题，请：

1. 检查硬件连接
2. 确认依赖包已安装
3. 查看错误日志
4. 参考文档或提交Issue
