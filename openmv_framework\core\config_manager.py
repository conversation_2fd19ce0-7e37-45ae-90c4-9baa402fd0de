#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置管理器

负责加载、保存和管理框架配置。
"""

import os
import json
from typing import Dict, Any, Optional

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config/"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件目录路径
        """
        self.config_path = config_path
        self.configs = {}
        
    def load_config(self, module_name: str, config_file: Optional[str] = None) -> Dict[str, Any]:
        """
        加载模块配置
        
        Args:
            module_name: 模块名称
            config_file: 配置文件名，如果为None则使用默认命名
            
        Returns:
            配置字典
        """
        if config_file is None:
            config_file = f"{module_name}_config.json"
            
        config_path = os.path.join(self.config_path, config_file)
        
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.configs[module_name] = config
                    return config
            else:
                print(f"配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            print(f"加载配置失败: {e}")
            return {}
            
    def save_config(self, module_name: str, config: Dict[str, Any], config_file: Optional[str] = None):
        """
        保存模块配置
        
        Args:
            module_name: 模块名称
            config: 配置字典
            config_file: 配置文件名，如果为None则使用默认命名
        """
        if config_file is None:
            config_file = f"{module_name}_config.json"
            
        config_path = os.path.join(self.config_path, config_file)
        
        try:
            # 确保目录存在
            os.makedirs(self.config_path, exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
                
            self.configs[module_name] = config
            print(f"配置已保存: {config_path}")
        except Exception as e:
            print(f"保存配置失败: {e}")
            
    def get_global_config(self) -> Dict[str, Any]:
        """
        获取全局配置
        
        Returns:
            全局配置字典
        """
        return self.load_config("global", "global_config.json")
        
    def get_config(self, module_name: str) -> Dict[str, Any]:
        """
        获取模块配置
        
        Args:
            module_name: 模块名称
            
        Returns:
            配置字典
        """
        if module_name in self.configs:
            return self.configs[module_name]
        else:
            return self.load_config(module_name)
            
    def update_config(self, module_name: str, updates: Dict[str, Any]):
        """
        更新模块配置
        
        Args:
            module_name: 模块名称
            updates: 更新的配置项
        """
        config = self.get_config(module_name)
        config.update(updates)
        self.save_config(module_name, config)
