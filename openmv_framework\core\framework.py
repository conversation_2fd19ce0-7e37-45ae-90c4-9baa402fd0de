#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OpenMV框架主类

提供框架的核心功能和模块管理。
"""

import os
import json
from typing import Dict, List, Optional, Any

class OpenMVFramework:
    """OpenMV框架主类"""
    
    def __init__(self, config_path: str = "config/"):
        """
        初始化框架
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = {}
        self.modules = {}
        self.is_initialized = False
        
    def load_config(self, config_file: str = "global_config.json") -> bool:
        """
        加载配置文件
        
        Args:
            config_file: 配置文件名
            
        Returns:
            bool: 加载是否成功
        """
        try:
            config_full_path = os.path.join(self.config_path, config_file)
            if os.path.exists(config_full_path):
                with open(config_full_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                return True
            else:
                print(f"配置文件不存在: {config_full_path}")
                return False
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return False
    
    def register_module(self, name: str, module: Any) -> bool:
        """
        注册模块
        
        Args:
            name: 模块名称
            module: 模块实例
            
        Returns:
            bool: 注册是否成功
        """
        try:
            self.modules[name] = module
            return True
        except Exception as e:
            print(f"注册模块失败: {e}")
            return False
    
    def get_module(self, name: str) -> Optional[Any]:
        """
        获取模块
        
        Args:
            name: 模块名称
            
        Returns:
            模块实例或None
        """
        return self.modules.get(name)
    
    def list_available_modules(self) -> List[str]:
        """
        列出可用模块
        
        Returns:
            模块名称列表
        """
        return list(self.modules.keys())
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取框架状态
        
        Returns:
            状态信息字典
        """
        return {
            "initialized": self.is_initialized,
            "config_loaded": bool(self.config),
            "modules_count": len(self.modules),
            "modules": list(self.modules.keys())
        }
    
    def initialize(self) -> bool:
        """
        初始化框架
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 加载配置
            if not self.load_config():
                return False
            
            # 初始化各个模块
            for module_name, module in self.modules.items():
                if hasattr(module, 'init'):
                    if not module.init():
                        print(f"模块初始化失败: {module_name}")
                        return False
            
            self.is_initialized = True
            return True
        except Exception as e:
            print(f"框架初始化失败: {e}")
            return False
    
    def cleanup(self):
        """清理框架资源"""
        try:
            # 清理各个模块
            for module_name, module in self.modules.items():
                if hasattr(module, 'cleanup'):
                    module.cleanup()
            
            self.modules.clear()
            self.is_initialized = False
        except Exception as e:
            print(f"框架清理失败: {e}")
