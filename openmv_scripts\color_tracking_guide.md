# 颜色追踪示例使用指南

## 📁 文件说明

我们创建了3个不同复杂度的颜色追踪示例：

### 1. `simple_red_tracking.py` ⭐ 推荐新手
- **最简单易用**
- 只追踪红色物体
- 显示绿色框和坐标
- 代码最少，容易理解

### 2. `color_tracking.py` 
- **功能完整**
- 追踪红色物体
- 显示位置、大小、距离估算
- 包含检测统计

### 3. `advanced_color_tracking.py`
- **功能最强大**
- 支持多种颜色追踪
- 可同时追踪多个目标
- 显示形状分析和FPS

## 🎯 功能介绍

### 主要功能：
1. **实时颜色检测** - 识别画面中的指定颜色物体
2. **目标框选** - 在检测到的物体周围画框
3. **位置显示** - 显示物体的X,Y坐标
4. **大小分析** - 显示物体的像素大小
5. **距离估算** - 根据物体大小估算距离
6. **多目标追踪** - 同时追踪多个物体（高级版本）

### 视觉效果：
- ✅ **绿色矩形框** - 标记检测到的物体
- ✅ **绿色十字** - 标记物体中心点
- ✅ **白色文字** - 显示坐标和信息
- ✅ **状态提示** - 显示检测状态

## 🚀 如何使用

### 步骤1：选择脚本
- 新手推荐：`simple_red_tracking.py`
- 想看更多功能：`color_tracking.py`
- 高级用户：`advanced_color_tracking.py`

### 步骤2：运行脚本
1. 在VSCode中打开选择的.py文件
2. 确保OpenMV-H7 R2已连接
3. 使用OpenMV扩展运行脚本
4. 查看Frame Buffer窗口

### 步骤3：测试效果
1. **准备红色物体**：
   - 红色的笔
   - 红色的纸
   - 红色的玩具
   - 红色的衣服

2. **将红色物体放在摄像头前**
3. **观察效果**：
   - 应该看到绿色框框住红色物体
   - 显示物体的坐标位置
   - 移动物体，框会跟随移动

## 🎮 操作技巧

### 获得最佳效果：
1. **光线充足** - 确保有足够的光线
2. **背景简单** - 避免复杂的背景
3. **颜色鲜艳** - 使用鲜艳的红色物体
4. **距离适中** - 物体距离摄像头30-100cm
5. **移动缓慢** - 缓慢移动物体以获得稳定追踪

### 如果检测不到：
1. **调整光线** - 增加或减少光线
2. **换个红色物体** - 尝试更鲜艳的红色
3. **调整距离** - 让物体更靠近或远离摄像头
4. **检查背景** - 确保背景中没有其他红色物体

## 🔧 参数调整

### 颜色阈值调整（高级用户）：
如果检测效果不好，可以修改颜色阈值：

```python
# 在脚本中找到这行：
red_threshold = (30, 100, 15, 127, 15, 127)

# 参数含义：(L_min, L_max, A_min, A_max, B_min, B_max)
# L: 亮度 (0-100)
# A: 绿色到红色 (-128到127)  
# B: 蓝色到黄色 (-128到127)
```

### 检测敏感度调整：
```python
# 修改这些参数来调整敏感度：
pixels_threshold=200    # 最小像素数，越小越敏感
area_threshold=200      # 最小区域面积，越小越敏感
```

## 🎉 预期效果

运行成功后，您应该看到：

1. **Frame Buffer窗口显示摄像头画面**
2. **红色物体被绿色框框住**
3. **物体中心有绿色十字标记**
4. **屏幕上显示坐标信息**
5. **物体移动时，框会实时跟随**

这就是一个完整的颜色追踪系统！可以作为更复杂视觉项目的基础。

---

**准备好了吗？选择一个脚本开始体验吧！** 🚀
