#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OpenMV框架基础示例 - Hello Framework

演示如何初始化和使用OpenMV框架的基本功能。
"""

import sys
import os

# 添加框架路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from openmv_framework import OpenMVFramework, get_info

def main():
    """主函数"""
    print("=" * 50)
    print("OpenMV Framework - Hello World 示例")
    print("=" * 50)
    
    # 显示框架信息
    info = get_info()
    print(f"框架名称: {info['name']}")
    print(f"版本: {info['version']}")
    print(f"作者: {info['author']}")
    print(f"描述: {info['description']}")
    print()
    
    try:
        # 初始化框架
        print("正在初始化框架...")
        framework = OpenMVFramework()
        
        # 加载配置
        print("正在加载配置...")
        framework.load_config()
        
        # 显示框架状态
        print("框架初始化完成!")
        print(f"框架状态: {framework.get_status()}")
        
        # 列出可用模块
        print("\n可用模块:")
        modules = framework.list_available_modules()
        for module in modules:
            print(f"  - {module}")
            
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    print("\nHello Framework 示例运行完成!")
    return 0

if __name__ == "__main__":
    exit(main())
