# OpenMV 基础相机 - 独立版本
# 适用于OpenMV-H7 R2，在VSCode中使用OpenMV扩展运行

import sensor, image, time

# 初始化摄像头
print("Initializing Basic Camera...")
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time=2000)
sensor.set_auto_gain(False)
sensor.set_auto_whitebal(False)
print("✅ Basic Camera initialized successfully")

# 计数器和时间
frame_count = 0
start_time = time.ticks_ms()

print("🎯 Basic Camera ready! Press Ctrl+C to stop.")
print("=" * 50)

try:
    while True:
        img = sensor.snapshot()
        frame_count += 1
        current_time = time.ticks_ms()
        
        # 显示帧数
        img.draw_string(10, 10, "Frame: " + str(frame_count), color=(255, 255, 255), scale=1)
        
        # 显示FPS
        elapsed = time.ticks_diff(current_time, start_time)
        if elapsed > 0:
            fps = frame_count * 1000 // elapsed
            img.draw_string(10, 30, "FPS: " + str(fps), color=(255, 255, 255), scale=1)
        
        # 显示模式信息
        img.draw_string(10, 220, "Mode: Basic Camera", color=(255, 255, 0), scale=1)

except KeyboardInterrupt:
    print("Basic Camera stopped")
