# Simple Red Object Tracking
# 简单红色物体追踪 - 最容易使用的版本

import sensor, image, time

# 红色阈值 (LAB色彩空间) - 已经调试好的参数
red_threshold = (30, 100, 15, 127, 15, 127)

# 初始化相机
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time = 2000)
sensor.set_auto_gain(False)
sensor.set_auto_whitebal(False)

print("Simple Red Tracking - Ready!")

while(True):
    img = sensor.snapshot()
    
    # 查找红色区域
    blobs = img.find_blobs([red_threshold], pixels_threshold=200, area_threshold=200)
    
    if blobs:
        # 找到最大的红色区域
        largest_blob = max(blobs, key=lambda b: b.pixels())
        
        # 画绿色矩形框
        img.draw_rectangle(largest_blob.rect(), color=(0, 255, 0), thickness=3)
        
        # 画中心十字
        img.draw_cross(largest_blob.cx(), largest_blob.cy(), color=(0, 255, 0), size=15, thickness=3)
        
        # 显示坐标
        coord_text = "(%d, %d)" % (largest_blob.cx(), largest_blob.cy())
        img.draw_string(largest_blob.cx() - 30, largest_blob.cy() - 30, coord_text, 
                       color=(255, 255, 255), scale=2)
    
    # 显示状态
    status = "RED FOUND!" if blobs else "Looking for red..."
    img.draw_string(10, 10, status, color=(255, 0, 0) if blobs else (255, 255, 255), scale=2)
