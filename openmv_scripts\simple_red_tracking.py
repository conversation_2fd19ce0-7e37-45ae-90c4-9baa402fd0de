# Simple Red Object Tracking
# 简单红色物体追踪 - 最容易使用的版本

import sensor, image, time

# 红色阈值 (LAB色彩空间) - 调整为更宽松的参数
red_threshold = (20, 100, 15, 127, 15, 127)

# 初始化相机
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time = 2000)
sensor.set_auto_gain(False)
sensor.set_auto_whitebal(False)

print("Simple Red Tracking - Ready!")
print("Hold a red object in front of camera")

frame_count = 0

while(True):
    img = sensor.snapshot()
    frame_count += 1

    # 查找红色区域 - 降低阈值让检测更容易
    blobs = img.find_blobs([red_threshold], pixels_threshold=50, area_threshold=50)

    # 调试信息 - 每30帧打印一次
    if frame_count % 30 == 0:
        print("Frame:", frame_count, "Blobs found:", len(blobs) if blobs else 0)

    if blobs:
        # 找到最大的红色区域
        largest_blob = max(blobs, key=lambda b: b.pixels())

        # 画绿色矩形框
        img.draw_rectangle(largest_blob.rect(), color=(0, 255, 0), thickness=3)

        # 画中心十字
        img.draw_cross(largest_blob.cx(), largest_blob.cy(), color=(0, 255, 0), size=15, thickness=3)

        # 显示坐标
        coord_text = "(%d, %d)" % (largest_blob.cx(), largest_blob.cy())
        img.draw_string(largest_blob.cx() - 30, largest_blob.cy() - 30, coord_text,
                       color=(255, 255, 255), scale=2)

        # 打印检测到的信息
        if frame_count % 10 == 0:
            print("RED DETECTED at:", largest_blob.cx(), largest_blob.cy(), "Size:", largest_blob.pixels())

    # 显示状态
    status = "RED FOUND!" if blobs else "Looking for red..."
    img.draw_string(10, 10, status, color=(255, 0, 0) if blobs else (255, 255, 255), scale=2)

    # 显示帧数
    frame_text = "Frame: %d" % frame_count
    img.draw_string(10, 220, frame_text, color=(255, 255, 0), scale=1)
