# OpenMV Color Tracking Example
# 红色物体追踪示例 - 在VSCode中使用OpenMV扩展运行

import sensor, image, time, math

# 颜色阈值设置 (LAB色彩空间)
# 红色阈值 - 可以根据实际情况调整
red_threshold = (30, 100, 15, 127, 15, 127)

# 初始化相机
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time = 2000)
sensor.set_auto_gain(False) # 关闭自动增益以获得更稳定的颜色检测
sensor.set_auto_whitebal(False) # 关闭自动白平衡

print("Red Object Tracking Started")
print("Hold a red object in front of camera")

# 统计变量
frame_count = 0
detected_count = 0

while(True):
    img = sensor.snapshot()
    frame_count += 1
    
    # 查找红色区域
    blobs = img.find_blobs([red_threshold], pixels_threshold=200, area_threshold=200)
    
    if blobs:
        detected_count += 1
        
        # 找到最大的红色区域
        largest_blob = max(blobs, key=lambda b: b.pixels())
        
        # 在最大区域周围画矩形框
        img.draw_rectangle(largest_blob.rect(), color=(0, 255, 0), thickness=2)
        
        # 在中心画十字标记
        img.draw_cross(largest_blob.cx(), largest_blob.cy(), color=(0, 255, 0), size=10, thickness=2)
        
        # 显示位置信息
        position_text = "X:%d Y:%d" % (largest_blob.cx(), largest_blob.cy())
        img.draw_string(10, 10, position_text, color=(255, 255, 255), scale=2)
        
        # 显示区域大小
        size_text = "Size:%d" % largest_blob.pixels()
        img.draw_string(10, 30, size_text, color=(255, 255, 255), scale=2)
        
        # 计算距离估算 (基于区域大小的简单估算)
        if largest_blob.pixels() > 1000:
            distance_text = "Distance: Close"
        elif largest_blob.pixels() > 500:
            distance_text = "Distance: Medium"
        else:
            distance_text = "Distance: Far"
        img.draw_string(10, 50, distance_text, color=(255, 255, 0), scale=1)
    
    # 显示检测统计
    if frame_count % 30 == 0:  # 每30帧显示一次统计
        detection_rate = (detected_count * 100) // frame_count
        print("Detection rate:", detection_rate, "%")
    
    # 在画面上显示状态
    status_text = "Tracking: " + ("ON" if blobs else "OFF")
    img.draw_string(10, 220, status_text, color=(255, 0, 0) if blobs else (128, 128, 128), scale=2)
