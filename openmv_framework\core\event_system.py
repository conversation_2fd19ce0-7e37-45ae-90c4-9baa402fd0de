#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
事件系统

提供模块间的事件驱动通信机制。
"""

from typing import Dict, List, Callable, Any
from collections import defaultdict

class EventSystem:
    """事件系统单例类"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(EventSystem, cls).__new__(cls)
            cls._instance.subscribers = defaultdict(list)
        return cls._instance
    
    @classmethod
    def get_instance(cls):
        """获取事件系统实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def subscribe(self, event_type: str, callback: Callable):
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        if callback not in self.subscribers[event_type]:
            self.subscribers[event_type].append(callback)
    
    def unsubscribe(self, event_type: str, callback: Callable):
        """
        取消订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        if callback in self.subscribers[event_type]:
            self.subscribers[event_type].remove(callback)
    
    def publish(self, event_type: str, data: Any = None):
        """
        发布事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        for callback in self.subscribers[event_type]:
            try:
                callback(data)
            except Exception as e:
                print(f"事件处理错误 {event_type}: {e}")
    
    def clear_subscribers(self, event_type: str = None):
        """
        清除订阅者
        
        Args:
            event_type: 事件类型，如果为None则清除所有
        """
        if event_type is None:
            self.subscribers.clear()
        else:
            self.subscribers[event_type].clear()
    
    def get_subscribers(self, event_type: str) -> List[Callable]:
        """
        获取事件订阅者列表
        
        Args:
            event_type: 事件类型
            
        Returns:
            订阅者列表
        """
        return self.subscribers[event_type].copy()
    
    def list_event_types(self) -> List[str]:
        """
        列出所有事件类型
        
        Returns:
            事件类型列表
        """
        return list(self.subscribers.keys())
