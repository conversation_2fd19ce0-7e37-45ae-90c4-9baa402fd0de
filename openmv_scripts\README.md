# OpenMV脚本文件

这个目录包含可以在VSCode中使用OpenMV扩展运行的脚本文件。

## 使用方法（VSCode + OpenMV扩展）

1. **连接OpenMV-H7 R2设备**到电脑USB端口
2. **在VSCode中打开**以下任一脚本文件：
   - `main.py` - 最简单的相机脚本（推荐）
   - `camera_test.py` - 极简版本
   - `hello_camera.py` - 基础相机脚本
   - `basic_camera.py` - 带FPS显示的完整脚本
3. **运行脚本**：
   - 按 `Ctrl+Shift+P` 打开命令面板
   - 输入 "OpenMV: Run Script" 或使用OpenMV扩展的运行按钮
4. **查看Frame Buffer窗口**看到摄像头画面

## 脚本说明

### main.py ⭐ 推荐
- 最简单的相机脚本
- 只有几行核心代码
- 适合初学者测试

### camera_test.py
- 极简版本
- 最基础的相机功能

### hello_camera.py
- 基础相机脚本
- 包含初始化信息输出
- 适合学习使用

### basic_camera.py
- 功能最完整的相机脚本
- 显示帧数和FPS
- 支持LCD显示屏
- 包含错误处理和统计信息

## 注意事项（VSCode环境）

1. 确保OpenMV-H7 R2设备已正确连接到USB端口
2. 确保VSCode已安装OpenMV扩展
3. 在VSCode中打开.py文件（不是在文件浏览器中）
4. 使用OpenMV扩展的命令或按钮运行脚本
5. Frame Buffer窗口会显示摄像头实时画面
6. 按Ctrl+C或使用扩展的停止按钮结束运行

## 故障排除

如果看不到画面：
1. 检查OpenMV设备USB连接
2. 确认VSCode的OpenMV扩展已正确识别设备
3. 重启VSCode或重新连接设备
4. 检查代码是否有语法错误
5. 确保在VSCode中运行脚本，而不是在系统中直接运行Python文件
