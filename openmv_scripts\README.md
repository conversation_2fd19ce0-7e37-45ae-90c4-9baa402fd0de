# OpenMV脚本文件

这个目录包含可以直接在OpenMV IDE中运行的脚本文件。

## 使用方法

1. **连接OpenMV-H7 R2设备**到电脑
2. **打开OpenMV IDE**
3. **打开以下任一脚本文件**：
   - `hello_camera.py` - 最简单的相机脚本
   - `basic_camera.py` - 带FPS显示的相机脚本
4. **点击运行按钮**（绿色播放按钮）
5. **查看Frame Buffer窗口**看到摄像头画面

## 脚本说明

### hello_camera.py
- 最简单的相机脚本
- 只有几行代码
- 适合初学者测试

### basic_camera.py  
- 功能更完整的相机脚本
- 显示帧数和FPS
- 支持LCD显示屏
- 包含错误处理

## 注意事项

1. 确保OpenMV设备已正确连接
2. 在OpenMV IDE中打开.py文件
3. 点击运行按钮开始执行
4. Frame Buffer窗口会显示摄像头画面
5. 按Ctrl+C或停止按钮结束运行

## 故障排除

如果看不到画面：
1. 检查摄像头连接
2. 确认设备连接正常
3. 重启OpenMV IDE
4. 检查代码是否有错误
