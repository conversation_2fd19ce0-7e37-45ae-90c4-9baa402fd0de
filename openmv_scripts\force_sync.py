# OpenMV 强制配置同步脚本
# 当发现配置文件和实际运行模式不一致时使用

print("=" * 60)
print("🔄 OpenMV 强制配置同步工具")
print("=" * 60)

try:
    # 1. 读取当前配置文件
    import config
    current_config = config.get_config()
    active_mode = current_config['active_mode']
    
    print(f"📋 配置文件显示的活动模式: {active_mode}")
    print(f"🔧 可用模式: {list(current_config['modes'].keys())}")
    
    # 2. 显示当前配置的详细信息
    if active_mode in current_config['modes']:
        mode_config = current_config['modes'][active_mode]
        print(f"\n📝 {active_mode} 模式的配置:")
        for key, value in mode_config.items():
            print(f"   {key}: {value}")
    
    # 3. 强制重新加载配置
    print(f"\n🔄 强制重新加载配置模块...")
    import sys
    if 'config' in sys.modules:
        del sys.modules['config']
        print("✅ 配置模块已从缓存中清除")
    
    # 重新导入
    import config
    reloaded_config = config.get_config()
    reloaded_mode = reloaded_config['active_mode']
    
    print(f"📋 重新加载后的活动模式: {reloaded_mode}")
    
    # 4. 检查是否有变化
    if active_mode != reloaded_mode:
        print(f"⚠️  检测到配置变化: {active_mode} -> {reloaded_mode}")
    else:
        print("✅ 配置一致，无变化")
    
    # 5. 提供同步建议
    print(f"\n💡 同步建议:")
    print(f"1. 确保您的OpenMV设备已连接")
    print(f"2. 在OpenMV IDE或VSCode中重新上传 main_dispatcher.py")
    print(f"3. 重新启动OpenMV设备上的程序")
    print(f"4. 当前配置文件设置为: {reloaded_mode}")
    
    # 6. 显示快速切换命令
    print(f"\n🔧 快速切换命令:")
    print(f"切换到红色追踪: 修改config.py中第9行为 'active_mode': 'red_tracking',")
    print(f"切换到基础相机: 修改config.py中第9行为 'active_mode': 'basic_camera',")
    
    print("=" * 60)
    print("✅ 同步检查完成")
    print("=" * 60)
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
