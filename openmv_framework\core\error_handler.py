#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
错误处理器

提供统一的错误处理和异常管理。
"""

import traceback
from typing import Optional, Callable, Any
from enum import Enum

class ErrorLevel(Enum):
    """错误级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, logger=None):
        """
        初始化错误处理器
        
        Args:
            logger: 日志记录器实例
        """
        self.logger = logger
        self.error_callbacks = {}
        
    def handle_error(self, error: Exception, level: ErrorLevel = ErrorLevel.ERROR, 
                    context: Optional[str] = None) -> bool:
        """
        处理错误
        
        Args:
            error: 异常对象
            level: 错误级别
            context: 错误上下文信息
            
        Returns:
            bool: 是否成功处理
        """
        try:
            error_msg = f"{context}: {str(error)}" if context else str(error)
            
            # 记录日志
            if self.logger:
                if level == ErrorLevel.DEBUG:
                    self.logger.debug(error_msg)
                elif level == ErrorLevel.INFO:
                    self.logger.info(error_msg)
                elif level == ErrorLevel.WARNING:
                    self.logger.warning(error_msg)
                elif level == ErrorLevel.ERROR:
                    self.logger.error(error_msg)
                elif level == ErrorLevel.CRITICAL:
                    self.logger.critical(error_msg)
            else:
                print(f"[{level.value}] {error_msg}")
            
            # 调用错误回调
            error_type = type(error).__name__
            if error_type in self.error_callbacks:
                self.error_callbacks[error_type](error, context)
            
            return True
        except Exception as e:
            print(f"错误处理器自身出错: {e}")
            return False
    
    def register_error_callback(self, error_type: str, callback: Callable):
        """
        注册错误回调函数
        
        Args:
            error_type: 错误类型名称
            callback: 回调函数
        """
        self.error_callbacks[error_type] = callback
    
    def unregister_error_callback(self, error_type: str):
        """
        取消注册错误回调函数
        
        Args:
            error_type: 错误类型名称
        """
        if error_type in self.error_callbacks:
            del self.error_callbacks[error_type]
    
    def safe_execute(self, func: Callable, *args, **kwargs) -> tuple:
        """
        安全执行函数
        
        Args:
            func: 要执行的函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            tuple: (是否成功, 结果或异常)
        """
        try:
            result = func(*args, **kwargs)
            return True, result
        except Exception as e:
            self.handle_error(e, ErrorLevel.ERROR, f"执行函数 {func.__name__}")
            return False, e
