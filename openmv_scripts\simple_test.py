# 最简单的测试 - 验证基本功能

print("=== Simple Test Start ===")

# 测试1: 基本Python功能
print("✅ Python is working")

# 测试2: 检查config.py文件是否存在
try:
    with open("config.py", "r") as f:
        content = f.read()
    print("✅ config.py file found")
    print("File size:", len(content), "characters")
except:
    print("❌ config.py file not found")

# 测试3: 尝试导入config
try:
    import config
    print("✅ config module imported")
except Exception as e:
    print("❌ config import failed:", str(e))

# 测试4: 检查DISPATCHER_CONFIG
try:
    from config import DISPATCHER_CONFIG
    print("✅ DISPATCHER_CONFIG found")
    print("Active mode:", DISPATCHER_CONFIG.get('active_mode', 'not found'))
except Exception as e:
    print("❌ DISPATCHER_CONFIG error:", str(e))

# 测试5: 检查get_config函数
try:
    from config import get_config
    result = get_config()
    print("✅ get_config() works")
    print("Result type:", type(result))
except Exception as e:
    print("❌ get_config() error:", str(e))

print("=== Simple Test Complete ===")
print("Please check the output above for any errors.")
