#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OpenMV-H7 R2 视觉开发框架安装配置
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    with open('README.md', 'r', encoding='utf-8') as f:
        return f.read()

# 读取requirements文件
def read_requirements():
    with open('requirements.txt', 'r', encoding='utf-8') as f:
        return [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name='openmv-framework',
    version='1.0.0',
    description='OpenMV-H7 R2 低耦合视觉开发框架',
    long_description=read_readme(),
    long_description_content_type='text/markdown',
    author='米醋电子工作室',
    author_email='<EMAIL>',
    url='https://github.com/your-repo/openmv-framework',
    license='MIT',
    
    # 包配置
    packages=find_packages(),
    include_package_data=True,
    zip_safe=False,
    
    # Python版本要求
    python_requires='>=3.7',
    
    # 依赖包
    install_requires=read_requirements(),
    
    # 可选依赖
    extras_require={
        'dev': [
            'pytest>=6.0',
            'pytest-cov>=2.0',
            'black>=21.0',
            'flake8>=3.8',
            'mypy>=0.800',
        ],
        'docs': [
            'sphinx>=4.0',
            'sphinx-rtd-theme>=0.5',
            'myst-parser>=0.15',
        ],
    },
    
    # 分类信息
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: Developers',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.7',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Topic :: Scientific/Engineering :: Image Recognition',
        'Topic :: Software Development :: Libraries :: Python Modules',
        'Topic :: System :: Hardware',
    ],
    
    # 关键词
    keywords='openmv, computer vision, machine learning, embedded, framework',
    
    # 项目URLs
    project_urls={
        'Bug Reports': 'https://github.com/your-repo/openmv-framework/issues',
        'Source': 'https://github.com/your-repo/openmv-framework',
        'Documentation': 'https://openmv-framework.readthedocs.io/',
    },
    
    # 入口点
    entry_points={
        'console_scripts': [
            'openmv-framework=openmv_framework.cli:main',
        ],
    },
    
    # 包数据
    package_data={
        'openmv_framework': [
            'config/*.json',
            'config/*.yaml',
            'templates/*.py',
        ],
    },
)
