# 测试配置读取功能
print("=== 配置读取测试 ===")

try:
    # 测试模块导入
    import config
    print("✅ Config module imported successfully")
    
    # 获取配置
    cfg = config.get_config()
    print("✅ Config loaded successfully")
    print("Current active_mode:", cfg['active_mode'])
    print("Available modes:", list(cfg['modes'].keys()))
    
    # 测试文件读取方法
    print("\n=== 文件读取测试 ===")
    config_paths = ["config.py", "openmv_scripts/config.py", "./config.py"]
    
    for path in config_paths:
        try:
            # 尝试不同的编码方式
            encodings = ['utf-8', 'gbk', 'cp1252', 'latin1']
            content = None
            used_encoding = None

            for encoding in encodings:
                try:
                    with open(path, "r", encoding=encoding) as f:
                        content = f.read()
                        used_encoding = encoding
                        break
                except UnicodeDecodeError:
                    continue
                except FileNotFoundError:
                    break

            if content:
                print(f"✅ Successfully read from: {path} (encoding: {used_encoding})")

                # 查找active_mode
                lines = content.split('\n')
                for line_num, line in enumerate(lines, 1):
                    if "'active_mode'" in line and ":" in line:
                        print(f"Found active_mode at line {line_num}: {line.strip()}")
                        # 提取值
                        parts = line.split(":")
                        if len(parts) > 1:
                            mode_part = parts[1].strip()
                            mode_part = mode_part.replace("'", "").replace('"', "").replace(",", "").strip()
                            print(f"Extracted mode: '{mode_part}'")
                        break
                break
            else:
                print(f"❌ Failed to read {path}: encoding issues or file not found")
        except Exception as e:
            print(f"❌ Failed to read {path}: {e}")
    
    print("\n=== 测试完成 ===")
    
except Exception as e:
    print("❌ Error:", e)
    import traceback
    traceback.print_exc()
