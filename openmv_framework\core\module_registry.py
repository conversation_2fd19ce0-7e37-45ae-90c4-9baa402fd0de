#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模块注册表

管理框架中所有模块的注册、发现和生命周期。
"""

from typing import Dict, List, Type, Any, Optional
from .base_module import BaseModule

class ModuleRegistry:
    """模块注册表"""
    
    def __init__(self):
        """初始化模块注册表"""
        self.registered_modules = {}  # 已注册的模块类
        self.active_modules = {}      # 活跃的模块实例
        self.module_dependencies = {} # 模块依赖关系
        
    def register_module_class(self, name: str, module_class: Type[BaseModule], 
                             dependencies: Optional[List[str]] = None):
        """
        注册模块类
        
        Args:
            name: 模块名称
            module_class: 模块类
            dependencies: 依赖的模块列表
        """
        if not issubclass(module_class, BaseModule):
            raise ValueError(f"模块类 {module_class} 必须继承自 BaseModule")
            
        self.registered_modules[name] = module_class
        self.module_dependencies[name] = dependencies or []
        
    def unregister_module_class(self, name: str):
        """
        注销模块类
        
        Args:
            name: 模块名称
        """
        if name in self.registered_modules:
            del self.registered_modules[name]
        if name in self.module_dependencies:
            del self.module_dependencies[name]
            
    def create_module(self, name: str, config: Optional[Dict[str, Any]] = None) -> Optional[BaseModule]:
        """
        创建模块实例
        
        Args:
            name: 模块名称
            config: 模块配置
            
        Returns:
            模块实例或None
        """
        if name not in self.registered_modules:
            print(f"未找到注册的模块: {name}")
            return None
            
        try:
            module_class = self.registered_modules[name]
            module_instance = module_class(config)
            self.active_modules[name] = module_instance
            return module_instance
        except Exception as e:
            print(f"创建模块实例失败 {name}: {e}")
            return None
    
    def get_module(self, name: str) -> Optional[BaseModule]:
        """
        获取模块实例
        
        Args:
            name: 模块名称
            
        Returns:
            模块实例或None
        """
        return self.active_modules.get(name)
    
    def destroy_module(self, name: str):
        """
        销毁模块实例
        
        Args:
            name: 模块名称
        """
        if name in self.active_modules:
            module = self.active_modules[name]
            module.cleanup()
            del self.active_modules[name]
    
    def list_registered_modules(self) -> List[str]:
        """
        列出已注册的模块
        
        Returns:
            模块名称列表
        """
        return list(self.registered_modules.keys())
    
    def list_active_modules(self) -> List[str]:
        """
        列出活跃的模块
        
        Returns:
            活跃模块名称列表
        """
        return list(self.active_modules.keys())
    
    def get_module_dependencies(self, name: str) -> List[str]:
        """
        获取模块依赖
        
        Args:
            name: 模块名称
            
        Returns:
            依赖模块列表
        """
        return self.module_dependencies.get(name, [])
    
    def resolve_dependencies(self, module_names: List[str]) -> List[str]:
        """
        解析模块依赖顺序
        
        Args:
            module_names: 要加载的模块列表
            
        Returns:
            按依赖顺序排列的模块列表
        """
        resolved = []
        visited = set()
        
        def visit(name):
            if name in visited:
                return
            visited.add(name)
            
            # 先处理依赖
            for dep in self.get_module_dependencies(name):
                if dep in module_names:
                    visit(dep)
            
            if name not in resolved:
                resolved.append(name)
        
        for name in module_names:
            visit(name)
            
        return resolved
    
    def get_registry_status(self) -> Dict[str, Any]:
        """
        获取注册表状态
        
        Returns:
            注册表状态信息
        """
        return {
            'registered_count': len(self.registered_modules),
            'active_count': len(self.active_modules),
            'registered_modules': list(self.registered_modules.keys()),
            'active_modules': list(self.active_modules.keys()),
            'dependencies': self.module_dependencies
        }
