#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OpenMV基础相机脚本
适用于OpenMV-H7 R2，可在OpenMV IDE中直接运行
"""

import sensor
import image
import time
import lcd

# 初始化相机传感器
def init_camera():
    """初始化相机设置"""
    sensor.reset()                      # 重置相机传感器
    sensor.set_pixformat(sensor.RGB565) # 设置像素格式为RGB565
    sensor.set_framesize(sensor.QVGA)   # 设置分辨率为QVGA (320x240)
    sensor.skip_frames(time=2000)       # 跳过前2秒的帧，让相机稳定
    sensor.set_auto_gain(False)         # 关闭自动增益
    sensor.set_auto_whitebal(False)     # 关闭自动白平衡
    
    print("相机初始化完成")
    print(f"分辨率: {sensor.width()}x{sensor.height()}")
    print(f"像素格式: RGB565")

# 初始化LCD显示屏（如果有的话）
def init_lcd():
    """初始化LCD显示屏"""
    try:
        lcd.init()
        lcd.clear()
        print("LCD初始化完成")
        return True
    except:
        print("未检测到LCD显示屏")
        return False

def main():
    """主函数"""
    print("=" * 40)
    print("OpenMV-H7 R2 基础相机脚本")
    print("=" * 40)
    
    # 初始化相机
    init_camera()
    
    # 初始化LCD
    has_lcd = init_lcd()
    
    # 帧计数器
    frame_count = 0
    start_time = time.ticks_ms()
    
    print("开始采集图像...")
    print("按Ctrl+C停止")
    
    while True:
        try:
            # 采集一帧图像
            img = sensor.snapshot()
            
            # 帧计数
            frame_count += 1
            
            # 在图像上绘制信息
            img.draw_string(10, 10, f"Frame: {frame_count}", color=(255, 0, 0), scale=2)
            
            # 计算FPS
            if frame_count % 30 == 0:
                current_time = time.ticks_ms()
                elapsed = time.ticks_diff(current_time, start_time)
                fps = (frame_count * 1000) // elapsed if elapsed > 0 else 0
                print(f"FPS: {fps}, 总帧数: {frame_count}")
            
            # 在图像上显示FPS信息
            if frame_count > 30:
                current_time = time.ticks_ms()
                elapsed = time.ticks_diff(current_time, start_time)
                fps = (frame_count * 1000) // elapsed if elapsed > 0 else 0
                img.draw_string(10, 30, f"FPS: {fps}", color=(0, 255, 0), scale=2)
            
            # 如果有LCD，显示图像
            if has_lcd:
                lcd.display(img)
            
            # 短暂延时
            time.sleep_ms(30)
            
        except KeyboardInterrupt:
            print("\n用户中断，停止采集")
            break
        except Exception as e:
            print(f"错误: {e}")
            time.sleep_ms(100)
    
    print("相机脚本结束")

# 运行主函数
if __name__ == "__main__":
    main()
