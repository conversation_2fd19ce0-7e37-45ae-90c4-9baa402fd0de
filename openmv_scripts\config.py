# OpenMV Dispatcher Configuration
# 统一调度器配置文件 - 用户可以直接修改此文件来切换功能和调整参数
# 修改后程序会自动检测并应用新配置，无需重启

# ===== 主配置 =====
DISPATCHER_CONFIG = {
    # 当前活动模式 - 修改这里来切换功能
    # 可选值: 'basic_camera', 'red_tracking'
    'active_mode': 'red_tracking',
    
    # 配置检查间隔 (帧数) - 每隔多少帧检查一次配置变化
    'config_check_interval': 30,  # 更频繁检查，约1秒检查一次
    
    # 调试模式 - 全局调试开关
    'global_debug': True,
    
    # ===== 各模式的详细配置 =====
    'modes': {
        
        # ----- 基础相机模式配置 -----
        'basic_camera': {
            'description': '基础相机功能，显示实时画面、FPS和帧数',
            
            # 显示选项
            'show_fps': True,           # 是否显示FPS
            'show_frame_count': True,   # 是否显示帧数
            'show_timestamp': False,    # 是否显示时间戳
            
            # FPS计算设置
            'fps_update_frames': 30,    # 每隔多少帧更新一次FPS显示
            
            # 显示样式
            'text_color': (255, 255, 255),  # 文字颜色 (白色)
            'text_scale': 2,                # 文字大小
        },
        
        # ----- 红色追踪模式配置 -----
        'red_tracking': {
            'description': '红色物体检测和追踪，支持实时参数调整',
            
            # 颜色检测参数 (LAB色彩空间)
            # 格式: (L_min, L_max, A_min, A_max, B_min, B_max)
            # 调整这些值来改变检测的红色范围
            'threshold': (20, 100, 15, 127, 15, 127),
            
            # 检测参数
            'min_pixels': 50,           # 最小像素数阈值
            'min_area': 50,             # 最小区域面积
            'max_targets': 5,           # 最大检测目标数
            
            # 显示选项
            'show_coordinates': True,   # 是否显示坐标
            'show_size_info': True,     # 是否显示大小信息
            'show_detection_count': True, # 是否显示检测到的目标数量
            
            # 调试选项
            'debug': True,              # 调试模式开关
            'debug_interval': 10,       # 调试信息输出间隔(帧)
            'verbose_debug': False,     # 详细调试信息
            
            # 绘制样式
            'box_color': (0, 255, 0),   # 边框颜色 (绿色)
            'box_thickness': 3,         # 边框粗细
            'cross_color': (0, 255, 0), # 十字颜色 (绿色)
            'cross_size': 15,           # 十字大小
            'cross_thickness': 3,       # 十字粗细
            'text_color': (255, 255, 255), # 文字颜色 (白色)
            'text_scale': 2,            # 文字大小
            
            # 状态显示
            'show_status': True,        # 是否显示检测状态
            'found_color': (255, 0, 0), # 找到目标时的状态文字颜色 (红色)
            'searching_color': (255, 255, 255), # 搜索中的状态文字颜色 (白色)
        },
        
        # ----- 多颜色追踪模式配置 (预留) -----
        'color_tracking': {
            'description': '多颜色物体检测和追踪 (未来功能)',
            
            # 预定义颜色阈值
            'colors': {
                'red': (20, 100, 15, 127, 15, 127),
                'green': (30, 100, -64, -8, -32, 32),
                'blue': (0, 30, 0, 64, -128, 0),
                'yellow': (60, 100, -10, 10, 20, 127),
            },
            
            # 当前活动颜色
            'active_color': 'red',
            
            # 检测参数
            'min_pixels': 50,
            'max_targets_per_color': 3,
            
            # 显示选项
            'show_color_name': True,
            'different_colors_for_different_targets': True,
        }
    }
}

# ===== 配置验证函数 =====
def validate_config(config):
    """验证配置的有效性"""
    try:
        # 检查必需的键
        required_keys = ['active_mode', 'modes']
        for key in required_keys:
            if key not in config:
                print("Config Error: Missing required key:", key)
                return False
        
        # 检查活动模式是否存在
        active_mode = config['active_mode']
        if active_mode not in config['modes']:
            print("Config Error: Active mode", active_mode, "not found in modes")
            return False
        
        # 检查红色追踪的阈值格式
        if active_mode == 'red_tracking':
            threshold = config['modes']['red_tracking'].get('threshold')
            if threshold and len(threshold) != 6:
                print("Config Error: Red tracking threshold must have 6 values")
                return False
        
        return True
        
    except Exception as e:
        print("Config validation error:", str(e))
        return False

# ===== 配置加载函数 =====
def get_config():
    """获取当前配置"""
    if validate_config(DISPATCHER_CONFIG):
        return DISPATCHER_CONFIG
    else:
        print("Using default safe config due to validation errors")
        return get_default_config()

def get_default_config():
    """获取默认安全配置"""
    return {
        'active_mode': 'basic_camera',
        'config_check_interval': 100,
        'global_debug': True,
        'modes': {
            'basic_camera': {
                'show_fps': True,
                'show_frame_count': True,
                'text_color': (255, 255, 255),
                'text_scale': 2,
            }
        }
    }

# ===== 配置更新函数 =====
def update_active_mode(new_mode):
    """更新活动模式"""
    global DISPATCHER_CONFIG
    if new_mode in DISPATCHER_CONFIG['modes']:
        DISPATCHER_CONFIG['active_mode'] = new_mode
        print("Active mode updated to:", new_mode)
        return True
    else:
        print("Error: Mode", new_mode, "not found")
        return False

def update_red_threshold(new_threshold):
    """更新红色阈值"""
    global DISPATCHER_CONFIG
    if len(new_threshold) == 6:
        DISPATCHER_CONFIG['modes']['red_tracking']['threshold'] = new_threshold
        print("Red threshold updated to:", new_threshold)
        return True
    else:
        print("Error: Threshold must have 6 values")
        return False

# ===== 使用示例 =====
"""
使用方法:

1. 切换到红色追踪模式:
   DISPATCHER_CONFIG['active_mode'] = 'red_tracking'

2. 切换到基础相机模式:
   DISPATCHER_CONFIG['active_mode'] = 'basic_camera'

3. 调整红色阈值 (更宽松的检测):
   DISPATCHER_CONFIG['modes']['red_tracking']['threshold'] = (15, 100, 10, 127, 10, 127)

4. 调整红色阈值 (更严格的检测):
   DISPATCHER_CONFIG['modes']['red_tracking']['threshold'] = (25, 100, 20, 127, 20, 127)

5. 关闭调试信息:
   DISPATCHER_CONFIG['modes']['red_tracking']['debug'] = False

6. 调整检测敏感度:
   DISPATCHER_CONFIG['modes']['red_tracking']['min_pixels'] = 30  # 更敏感
   DISPATCHER_CONFIG['modes']['red_tracking']['min_pixels'] = 100 # 更不敏感

修改后保存文件，程序会自动检测并应用新配置！
"""
